package com.yxt.talent.rv.controller.manage.xpd.dimcomb.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * XpdDimCombPutCmd
 * <AUTHOR>
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "维度组合bean对象")
public class XpdDimCombPutCmd {

    @Schema(description = "id")
    private String id;

    /**
     * x轴维度id
     */
    @JsonProperty("xSdDimId")
    @Schema(description = "x轴维度id")
    @NotBlank(message = "apis.sptalentrv.xpd.dim.comb.xSdDimId.notnull")
    private String xSdDimId;

    /**
     * y轴维度id
     */
    @JsonProperty("ySdDimId")
    @Schema(description = "y轴维度id")
    @NotBlank(message = "apis.sptalentrv.xpd.dim.comb.ySdDimId.notnull")
    private String ySdDimId;

    /**
     * 组合名称
     */
    @Schema(description = "组合名称")
    @NotBlank(message = "apis.sptalentrv.xpd.dim.comb.combName.notnull")
    @Max(value = 200, message = "apis.sptalentrv.xpd.dim.comb.combName.maxlength")
    private String combName;

    /**
     * 描述
     */
    @Schema(description = "组合描述")
    @NotBlank(message = "apis.sptalentrv.xpd.dim.comb.combDesc.notnull")
    @Max(value = 2000, message = "apis.sptalentrv.xpd.dim.comb.combDesc.maxlength")
    private String combDesc;

}
