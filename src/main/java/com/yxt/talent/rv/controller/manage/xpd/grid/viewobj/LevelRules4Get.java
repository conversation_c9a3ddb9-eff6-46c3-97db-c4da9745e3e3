package com.yxt.talent.rv.controller.manage.xpd.grid.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
@Getter
@Setter
@Schema(description = "分层规则详情返回")
public class LevelRules4Get implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description="主键;id主键")
    private String id;
    @Schema(description="机构号;机构id")
    private String orgId;
    @Schema(description="分层名称;名称")
    private String name;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新时间")
    private Date updateTime;
    @Schema(description="创建人;创建人id")
    @JsonProperty("@createUserId")
    private AmUser4DTO createUserId;
    @Schema(description="更新人;更新人id")
    @JsonProperty("@updateUserId")
    private AmUser4DTO updateUserId;
    @Schema(description="宫格模板;按照业务需求,返回应用的实体字段")
    @JsonProperty("@gridid")
    private AmSlDrawer4RespDTO gridid;
    @Schema(description="是否胜任")
    private Integer competent;
    @Schema(description="分层规则")
    private String levelrule;
}
