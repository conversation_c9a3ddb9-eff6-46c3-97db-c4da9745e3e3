package com.yxt.talent.rv.controller.manage.activity;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.talent.rv.application.activity.ActivityCalcComponent;
import com.yxt.talent.rv.controller.manage.activity.viewobj.ActvCalcStatusVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

/**
 * <AUTHOR>
 * @since 2024/12/19
 */
@Slf4j
@RestController
@RequestMapping("/activity/common")
@RequiredArgsConstructor
@Tag(name = "活动通用接口", description = "活动通用接口")
public class ActivityCommonController {

    private final AuthService authService;
    private final ActivityCalcComponent activityCalcComponent;

    @Operation(summary = "动态人才评估-项目统计信息")
    @GetMapping(value = "/{actId}/{actType}/calc/status")
    @Parameters({@Parameter(name = "actId", description = "活动ID", in = ParameterIn.PATH, required = true),
            @Parameter(name = "actType", description = "0-盘点计算 1-动态人才评估 CalcLogTypeEnum", in = ParameterIn.PATH, required = true)})
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public Integer getActvProfCalcStatus(HttpServletRequest request, @PathVariable String actId,
            @PathVariable Integer actType) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return activityCalcComponent.checkActCalcStatus(userCache.getOrgId(), actId, actType);
    }

}
