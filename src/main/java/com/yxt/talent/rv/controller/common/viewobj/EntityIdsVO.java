package com.yxt.talent.rv.controller.common.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;

import java.util.List;

/**
 * 通用ID列表表单
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "IdsForm", description = "通用ID列表表单")
public class EntityIdsVO {
    /**
     * ID列表
     */
    @Schema(description = "ID列表")
    @NotEmpty(message = "ID列表不能为空")
    private List<String> ids;
} 