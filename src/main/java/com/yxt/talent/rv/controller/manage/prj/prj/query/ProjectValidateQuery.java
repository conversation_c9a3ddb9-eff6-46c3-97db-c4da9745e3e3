package com.yxt.talent.rv.controller.manage.prj.prj.query;

import com.yxt.criteria.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "盘点项目重名校验传参对象")
public class ProjectValidateQuery implements Query {

    @Schema(description = "项目id:新增时不需要传递，编辑时需要传递")
    private String projectId;

    @Schema(description = "项目名称")
    @NotBlank(message = "apis.sptalentrv.prj.name.notBlank")
    private String projectName;
}
