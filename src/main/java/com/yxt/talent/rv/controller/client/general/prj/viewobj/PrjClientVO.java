package com.yxt.talent.rv.controller.client.general.prj.viewobj;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 盘点结果【项目维度】列表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class PrjClientVO {
    @Schema(description = "项目Id")
    private String id;

    @Schema(description = "xpdId, 盘点项目ID")
    private String xpdId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "计划开始时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2DAY, timezone = Constants.STR_GMT8)
    private Date startTime;

    @Schema(description = "计划结束时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2DAY, timezone = Constants.STR_GMT8)
    private Date endTime;

    @Schema(description = "项目状态（0-未发布草稿，1-未开始，2-进行中，3-已结束")
    private int projectStatus;

    @Schema(description = "团队成员数量")
    private int memberCount;

    @Schema(description = "盘点进度")
    private BigDecimal progress;
}
