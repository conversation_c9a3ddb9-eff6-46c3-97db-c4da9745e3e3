package com.yxt.talent.rv.controller.manage.xpd.dimcomb.command;

import com.yxt.modelhub.api.bean.dto.AmSlDrawer4ReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
@Getter
@Setter
@Schema(description = "维度组合更新请求")
public class Dimcomb4Update implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="维度组合名称;名称", requiredMode = Schema.RequiredMode.REQUIRED )
    @NotBlank
    @Size(max=200)
    private String name;

    @Schema(description="x轴", requiredMode = Schema.RequiredMode.REQUIRED )
    @NotEmpty
    private List<AmSlDrawer4ReqDTO> xsddimid;

    @Schema(description="y轴", requiredMode = Schema.RequiredMode.REQUIRED )
    @NotEmpty
    private List<AmSlDrawer4ReqDTO> ysddimid;

    @Schema(description="描述")
    private String combdesc;

    @Schema(description="来源类型:0-内置,1-自建", requiredMode = Schema.RequiredMode.REQUIRED )
    @NotBlank
    @Size(max=100)
    private String combtype = "1";

}
