package com.yxt.talent.rv.controller.openapi.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.util.Collection;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "绩效同步集合")
public class PerfSyncV2OpenCmdList {

    @Schema(description = "事务ID，用于多次并发同步时的幂等性校验", requiredMode = REQUIRED)
    private String tranId;

    @Valid
    @Schema(description = "员工绩效数据，每次支持同步100条", requiredMode = REQUIRED)
    @Size(min = 1, max = 100, message = "apis.sptalentrv.transmit.import.data.out.limit")
    Collection<PerfSyncV2OpenCmd> datas;

}
