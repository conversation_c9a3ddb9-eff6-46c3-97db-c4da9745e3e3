package com.yxt.talent.rv.controller.manage.dmp.command;

import com.yxt.criteria.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class DmpRuleScoreSaveCmd implements Command {

    @Schema(description = "维度分设置主键ID")
    private String id;

    @Schema(description = "维度ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dimId;

    @Schema(description = "维度权重", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer weight;


    private String dimName;

    @Schema(description = "已删除，0-未删除 1-已删除")
    private BigDecimal formMaxValue;

    @Schema(description = "已删除，0-未删除 1-已删除")
    private BigDecimal formMinValue;

    @Schema(description = "是否来自form，1-是，0-否")
    private Boolean isForm;

    @Schema(description = "维度分值明细", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DmpRuleScoreDetailSaveCmd> ruleScoreDetailList;

    @Schema(description = "事务ID，用于幂等性校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tranId;

}
