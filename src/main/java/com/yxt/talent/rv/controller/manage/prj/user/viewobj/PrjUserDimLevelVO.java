package com.yxt.talent.rv.controller.manage.prj.user.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class PrjUserDimLevelVO {
    @Schema(description = "维度id")
    private String dimensionId;

    @Schema(description = "维度名称")
    private String dimensionName;

    @Schema(description = "维度等级 0-默认，1-低，2-中，3-高")
    private int level;

    @Schema(description = "类型（1:绩效 2:能力，3:潜力，4-机构自定义）")
    private Integer dimensionType;
}
