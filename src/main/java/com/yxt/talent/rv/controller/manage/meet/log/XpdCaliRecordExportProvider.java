package com.yxt.talent.rv.controller.manage.meet.log;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.meet.command.NewCaliMeetUserDelCmd;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetOptStatusLogVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Component
@RequiredArgsConstructor
public class XpdCaliRecordExportProvider implements AuditLogDataProvider<String, String> {
    private final XpdService xpdService;

    @Override
    public String before(String param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public String after(String param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public String convertParam(Object param, AuditLogBasicBean logBasic) {
        if (Objects.isNull(param)) {
            return null;
        }
        return param.toString();
    }

    @Override
    public Pair<String, String> entityInfo(String param, String beforeObj, String afterObj,
            AuditLogBasicBean logBasic) {
        String xpdName = "";
        if (StringUtils.isNotBlank(param)) {
            Activity activity = xpdService.findAomPrjByAomId(logBasic.getOrgId(), param);
            xpdName = activity.getActvName();
        }
        return Pair.of(param, String.format("盘点-%s-导出校准记录", xpdName));
    }
}
