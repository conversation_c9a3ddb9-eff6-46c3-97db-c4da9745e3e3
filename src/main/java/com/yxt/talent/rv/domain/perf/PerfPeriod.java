package com.yxt.talent.rv.domain.perf;

import com.yxt.AuditAggregateRoot;
import jakarta.annotation.Nonnull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 绩效周期表
 */
@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class PerfPeriod extends AuditAggregateRoot<String, LocalDateTime> {

    // 机构id
    @Nonnull
    @EqualsAndHashCode.Include
    private String orgId;

    // 周期名称
    @EqualsAndHashCode.Include
    private String periodName;

    // 排序
    private Integer orderIndex;

    // 绩效周期类型（老数据该字段可能为空）：0-月度绩效, 1-季度绩效, 2-半年度，3-年度
    private Integer cycle;

    // 月度:1-12, 季度:1-4, 半年度:1-2,年份:20xx
    private Integer period;

    // 绩效年份，格式：YYYY
    private Integer yearly;

    // 绩效总分
    private BigDecimal scoreTotal;

    /**
     * 根据周期类型和周期值构建周期名称
     */
    public String buildPeriodName() {
        if (cycle == null || period == null) {
            return "";
        }
        periodName = switch (cycle) {
            case 0 -> yearly + "年" + toCnMonth(period) + "月";
            case 1 -> yearly + "年第" + toCnQuarter(period) + "季度";
            case 2 -> yearly + "年" + toCnHalfYear(period) + "半年度";
            case 3 -> period + "年度";
            default -> "";
        };
        return periodName;
    }

    private static String toCnHalfYear(Integer period) {
        return switch (period) {
            case 1 -> "上";
            case 2 -> "下";
            default -> "";
        };
    }

    private static String toCnQuarter(Integer period) {
        return switch (period) {
            case 1 -> "一";
            case 2 -> "二";
            case 3 -> "三";
            case 4 -> "四";
            default -> "";
        };
    }

    private static String toCnMonth(Integer period) {
        return switch (period) {
            case 1 -> "一";
            case 2 -> "二";
            case 3 -> "三";
            case 4 -> "四";
            case 5 -> "五";
            case 6 -> "六";
            case 7 -> "七";
            case 8 -> "八";
            case 9 -> "九";
            case 10 -> "十";
            case 11 -> "十一";
            case 12 -> "十二";
            default -> "";
        };
    }
}
