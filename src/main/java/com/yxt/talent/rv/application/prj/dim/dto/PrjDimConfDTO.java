package com.yxt.talent.rv.application.prj.dim.dto;

import com.yxt.common.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 维度配置关系表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class PrjDimConfDTO {
    // 主键
    private String id;
    // 机构id
    private String orgId;
    // 项目id
    private String projectId;
    // 维度id
    private String dimensionId;
    // 维度名称
    private String dimensionName;
    // 维度类型
    private Integer dimensionType;
    // 维度状态（0-未配置，1-已配置）
    private Integer dimensionStatus;
    // 模型id
    private String modelId;
    // 盘点工具id（绩效id或测评id）
    private String toolId;
    // 盘点工具类型（0-未配置，1-绩效，2-测评，3-盘点数据导入）
    private Integer toolType;
    // 排序，默认从0开始
    private Integer orderIndex;
    // 创建人主键
    private String createUserId;

    // 创建时间
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    private String updateUserId;

    // 更新时间
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;

    @Schema(description = "盘点工具来源（0-自建，1-同模）")
    private Integer toolSource;

    @Schema(description = "盘点工具-同模测评是否来自于测评中心，0-否，1-是")
    private Integer evalRefCenter;
}
