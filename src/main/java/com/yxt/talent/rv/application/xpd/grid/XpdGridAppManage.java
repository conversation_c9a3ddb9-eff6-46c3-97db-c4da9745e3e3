package com.yxt.talent.rv.application.xpd.grid;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.talent.rv.application.xpd.dimcomb.XpdDimCombAppService;
import com.yxt.talent.rv.application.xpd.common.dto.XpdInfoDto;
import com.yxt.talent.rv.controller.common.viewobj.XpdGridVO;
import com.yxt.talent.rv.controller.manage.prj.dim.viewobj.PrjUserDimConfVO;
import com.yxt.talent.rv.controller.manage.xpd.grid.command.*;
import com.yxt.talent.rv.controller.manage.xpd.grid.dto.XpdGridDimCombDTO;
import com.yxt.talent.rv.controller.manage.xpd.grid.query.XpdGridQuery;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.*;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombInfoVO;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.ApassEntityUtils;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 宫格管理
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class XpdGridAppManage {
    private final XpdGridMapper gridMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final XpdGridDimCombMapper gridDimCombMapper;
    private final XpdGridAppService gridAppService;
    private final XpdGridCellMapper gridCellMapper;
    private final XpdGridLevelMapper gridLevelMapper;
    private final XpdGridRatioMapper gridRatioMapper;
    private final XpdDimCombMapper dimCombMapper;
    private final XpdMapper xpdMapper;
    private final SpsdAclService spsdAclService;
    private final XpdGridAppService xpdGridAppService;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final XpdDimCombAppService xpdDimCombAppService;
    private final AppProperties appProperties;
    private final XpdDimMapper xpdDimMapper;

    private XpdGridQuery cover4Page(SearchDTO bean){
        XpdGridQuery query = new XpdGridQuery();
        QueryUtil.Search search = QueryUtil.parse(bean);
        Map<String, String> filterEq = search.getFilterEq();
        String sourcetype = filterEq.get("sourcetype");
        if (StringUtils.isNotBlank(sourcetype)){
            query.setSourceType(Integer.parseInt(sourcetype));
        }
        String gridtype = filterEq.get("gridtype");
        if (StringUtils.isNotBlank(gridtype)){
            query.setGridType(Integer.parseInt(gridtype));
        }
        String gridstate = filterEq.get("gridstate");
        if (StringUtils.isNotBlank(gridstate)){
            query.setGridState(Integer.parseInt(gridstate));
        }
        QueryUtil.SearchQuery searchLike = search.getSearchLike();
        query.setName(searchLike.getValue());
        return query;
    }
    public PagingList<RvGrid4Get> gridPage(String orgId, PageRequest page, SearchDTO bean) {

        Page<XpdGridPO> pageParam = ApiUtil.toPage(page);
        XpdGridQuery query = cover4Page(bean);
        query.setName(ApiUtil.getFiltedLikeString(query.getName()));
        IPage<XpdGridPO> gridPage = gridMapper.selectPage(pageParam, orgId, query);
        PagingList<XpdGridPO> gridPagingList = BeanCopierUtil.toPagingList(gridPage);
        PagingList<RvGrid4Get> resPage = new PagingList<>();
        resPage.setPaging(gridPagingList.getPaging());
        resPage.setDatas(new ArrayList<>());
        if (CollectionUtils.isEmpty(gridPagingList.getDatas())) {
            return resPage;
        }
        Set<String> userIds = gridPagingList.getDatas().stream().map(XpdGridPO::getCreateUserId).collect(Collectors.toSet());
        List<UdpLiteUserPO> udpLiteUserPOS = udpLiteUserMapper.selectByUserIds(orgId, new ArrayList<>(userIds));
        Map<String, UdpLiteUserPO> udpMap = StreamUtil.list2map(udpLiteUserPOS, UdpLiteUserPO::getId);

        List<RvGrid4Get> resList = new ArrayList<>();
        for (XpdGridPO data : gridPagingList.getDatas()) {
            RvGrid4Get res = new RvGrid4Get();
            res.setId(data.getId());
            res.setName(data.getGridName());
            res.setGridtype(String.valueOf(data.getGridType()));
            res.setGriddesc(data.getGridDesc());
            res.setSourcetype(String.valueOf(data.getSourceType()));
            res.setGridstate(String.valueOf(data.getGridState()));
            // 创建人
            UdpLiteUserPO udpLiteUserPO = udpMap.get(data.getCreateUserId());
            if (udpLiteUserPO != null) {
                res.setCreateUserId(ApassEntityUtils.createDrawer4UserRespDTO(udpLiteUserPO.getFullname(), udpLiteUserPO.getId()));
            }
           res.setCreateTime(DateTimeUtil.makeLocalDateTime2Date(data.getCreateTime()));
           resList.add(res);
        }
        resPage.setDatas(resList);

        return resPage;
    }

    public List<XpdGridListVO> gridList(String orgId) {
        XpdGridQuery query = new XpdGridQuery();
        query.setGridState(1);
        List<XpdGridPO> gridList = gridMapper.selectList(orgId, query);
        if(CollectionUtils.isEmpty(gridList)) {
            return new ArrayList<>();
        }
        Set<String> userIds = gridList.stream().map(XpdGridPO::getCreateUserId).collect(Collectors.toSet());
        List<UdpLiteUserPO> udpLiteUserPOS = udpLiteUserMapper.selectByUserIds(orgId, new ArrayList<>(userIds));
        Map<String, UdpLiteUserPO> udpMap = StreamUtil.list2map(udpLiteUserPOS, UdpLiteUserPO::getId);
        List<XpdGridListVO> resList = new ArrayList<>();
        for (XpdGridPO data : gridList) {
            XpdGridListVO res = new XpdGridListVO();
            res.setId(data.getId());
            res.setGridName(data.getGridName());
            res.setGridType(data.getGridType());
            res.setGridDesc(data.getGridDesc());
            res.setSourceType(data.getSourceType());
            res.setGridState(data.getGridState());
            // 创建人
            UdpLiteUserPO udpLiteUserPO = udpMap.get(data.getCreateUserId());
            if (udpLiteUserPO != null) {
                res.setCreateUser(udpLiteUserPO.getFullname());
            }
            res.setCreateTime(DateTimeUtil.makeLocalDateTime2Date(data.getCreateTime()));
            resList.add(res);
        }

        return resList;
    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public String createGrid(String orgId, String userId, RvGrid4Create cmd){
        XpdGridCreateCmd bean = coverGrid4Create(cmd);

        // 名称重复校验
        String name = cmd.getName();
        xpdGridAppService.chkName(orgId, name);

        XpdGridPO xpdGrid = new XpdGridPO();
        String gridId = ApiUtil.getUuid();
        xpdGrid.setId(gridId);
        xpdGrid.setXpdId(AppConstants.TEMPLATE_XPD_ID);
        xpdGrid.setOrgId(orgId);
        xpdGrid.setGridName(bean.getGridName());
        xpdGrid.setGridType(bean.getGridType());
        xpdGrid.setConfigType(bean.getConfigType());
        xpdGrid.setTemplate(0);
        xpdGrid.setSourceType(1);
        xpdGrid.setGridState(0);
        xpdGrid.setGridDesc(bean.getGridDesc());
        xpdGrid.setDeleted(0);
        EntityUtil.setAuditFields(xpdGrid, userId);

        // 维度组合
        if (CollectionUtils.isEmpty(bean.getXpdGridCombCmds())) {
            throw new ApiException("apis.sptalentrv.xpd.grid.comb.empty");
        }
        // 校验只能有一个默认显示
        xpdGridAppService.chkDimCombShow(bean.getXpdGridCombCmds());
        // 校验度组合不能重复
        xpdGridAppService.chkDimCombRepeat(bean.getXpdGridCombCmds());

        List<XpdGridDimCombPO> list = new ArrayList<>();
        int index =  1;
        for (XpdGridCombCmd xpdGridCombCmd : bean.getXpdGridCombCmds()) {
            XpdGridDimCombPO gridDimComb = new XpdGridDimCombPO();
            gridDimComb.setId(ApiUtil.getUuid());
            gridDimComb.setOrgId(orgId);
            gridDimComb.setXpdId(AppConstants.TEMPLATE_XPD_ID);
            gridDimComb.setGridId(gridId);
            gridDimComb.setDimCombId(xpdGridCombCmd.getDimCombId());
            gridDimComb.setShowType(xpdGridCombCmd.getShowType());
            gridDimComb.setDeleted(0);
            gridDimComb.setOrderIndex(index++);
            EntityUtil.setAuditFields(gridDimComb, userId);
            list.add(gridDimComb);
        }

        gridMapper.insert(xpdGrid);
        if (CollectionUtils.isNotEmpty(list)) {
            gridDimCombMapper.insertBatch(list);
        }
        // 初始化 宫格
        gridAppService.createGridCell(xpdGrid, list, userId, AppConstants.TEMPLATE_XPD_ID);

        // 初始化，人才分层，宫格层级
        gridAppService.createGridLevel(xpdGrid, userId, 1, AppConstants.TEMPLATE_XPD_ID);
       /* // 初始化，人才分层
        gridAppService.initXpdLevel(xpdGrid, userId);*/
        return gridId;
    }

    private XpdGridCreateCmd coverGrid4Create(RvGrid4Create cmd){
        XpdGridCreateCmd bean = new XpdGridCreateCmd();
        bean.setGridName(cmd.getName());
        bean.setGridType(Integer.parseInt(cmd.getGridtype()));
        bean.setConfigType(Integer.parseInt(cmd.getConfigtype()));
        bean.setGridDesc(cmd.getGriddesc());
        //
        RvGrid4CreateChildCmd childs = cmd.get_childs();
        List<GridCombCmd> gridComb = childs.getGrid_comb();
        List<XpdGridCombCmd> xpdGridCombCmds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(gridComb)) {
            for (GridCombCmd gridCombCmd : gridComb) {
                XpdGridCombCmd gridCombin = new XpdGridCombCmd();
                gridCombin.setShowType(gridCombCmd.getShowtype());
                gridCombin.setDimCombId(gridCombCmd.getCombname().get(0).getId());
                xpdGridCombCmds.add(gridCombin);
            }
        }
        bean.setXpdGridCombCmds(xpdGridCombCmds);
        return bean;
    }

    private XpdGridCreateCmd cover4GridEdit(String gridId, RvGrid bean){
        XpdGridCreateCmd cmd = new XpdGridCreateCmd();
        cmd.setId(gridId);
        cmd.setGridName(bean.getName());
        cmd.setConfigType(Integer.parseInt(bean.getConfigtype()));
        cmd.setGridDesc(bean.getGriddesc());

        List<XpdGridCombCmd> xpdGridCombCmds = new ArrayList<>();
        RvGrid4CreateChildCmd childs = bean.get_childs();
        List<GridCombCmd> gridComb = childs.getGrid_comb();
        Validate.isNotEmpty(gridComb, "apis.sptalentrv.xpd.grid.not.exist");
        for (GridCombCmd gridCombCmd : gridComb) {
            XpdGridCombCmd xpdGridCombCmd = new XpdGridCombCmd();
            GridCombname gridCombname = gridCombCmd.getCombname().get(0);
            xpdGridCombCmd.setDimCombId(gridCombname.getId());
            xpdGridCombCmd.setShowType(gridCombCmd.getShowtype());
            xpdGridCombCmds.add(xpdGridCombCmd);
        }
        cmd.setXpdGridCombCmds(xpdGridCombCmds);

        return cmd;
    }

    public void editGrid(String orgId, String userId, RvGrid cmd, String gridId){
        XpdGridCreateCmd bean = cover4GridEdit(gridId, cmd);
        String id = bean.getId();
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(id);

        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");
        Integer configType = xpdGrid.getConfigType();

        if (xpdGrid.getTemplate() == 0 && xpdGrid.getGridState() == 1) {
            throw new ApiException("apis.sptalentrv.xpd.grid.publish");
        }

        // 宫格类型不能修改
        xpdGrid.setGridName(bean.getGridName());
        xpdGrid.setGridDesc(bean.getGridDesc());
        if (CollectionUtils.isEmpty(bean.getXpdGridCombCmds())) {
            throw new ApiException("apis.sptalentrv.xpd.grid.comb.empty");
        }
        gridAppService.chkDimCombShow(bean.getXpdGridCombCmds());
        xpdGridAppService.chkDimCombRepeat(bean.getXpdGridCombCmds());

        //
        List<XpdGridDimCombPO> grIdDimCombList =
            gridDimCombMapper.findTempByGridId(xpdGrid.getOrgId(), xpdGrid.getId());
        List<String> fridDimCombIds = grIdDimCombList.stream().map(XpdGridDimCombPO::getId).toList();

        List<String> existDimCombIds = grIdDimCombList.stream().map(XpdGridDimCombPO::getDimCombId).toList();

        List<XpdGridCombCmd> xpdGridCombCmds = bean.getXpdGridCombCmds();
        List<String> inputDimCombIds = xpdGridCombCmds.stream().map(XpdGridCombCmd::getDimCombId)
            .filter(StringUtils::isNotBlank)
            .toList();
        // 需要删除的id
        List<String> deletedDimCombIds = existDimCombIds.stream().filter( x -> !inputDimCombIds.contains(x)).toList();

        Map<String, XpdGridDimCombPO> gridDimCombMap =
            StreamUtil.list2map(grIdDimCombList, XpdGridDimCombPO::getDimCombId);
        List<XpdGridDimCombPO> updateList = new ArrayList<>();
        List<XpdGridDimCombPO> addList = new ArrayList<>();
        String xpdId = AppConstants.TEMPLATE_XPD_ID;
        if (xpdGrid.getTemplate() == 1) {
            xpdId = xpdGrid.getXpdId();
        }
        int combIndex = 1;
        for (XpdGridCombCmd xpdGridCombCmd : bean.getXpdGridCombCmds()) {
            XpdGridDimCombPO gridDimCombPO = gridDimCombMap.get(xpdGridCombCmd.getDimCombId());
            if (gridDimCombPO != null) {
                gridDimCombPO.setOrderIndex(combIndex++);
                gridDimCombPO.setShowType(xpdGridCombCmd.getShowType());
                EntityUtil.setUpdate(gridDimCombPO, userId);
                updateList.add(gridDimCombPO);
            } else {
                // 新增
                XpdGridDimCombPO xpdGridDimComb= new XpdGridDimCombPO();
                xpdGridDimComb.setId(ApiUtil.getUuid());
                xpdGridDimComb.setOrgId(xpdGrid.getOrgId());
                xpdGridDimComb.setXpdId(xpdId);
                xpdGridDimComb.setGridId(xpdGrid.getId());
                xpdGridDimComb.setDimCombId(xpdGridCombCmd.getDimCombId());
                xpdGridDimComb.setShowType(xpdGridCombCmd.getShowType());
                xpdGridDimComb.setDeleted(0);
                xpdGridDimComb.setOrderIndex(combIndex++);
                EntityUtil.setAuditFields(xpdGridDimComb, userId);
                addList.add(xpdGridDimComb);
            }
        }
        // 层级编辑
        List<XpdGridLevelPO> updateLevelList = new ArrayList<>();
        // 新增的维度组合id
        if (CollectionUtils.isNotEmpty(addList) && xpdGrid.getConfigType() == 1) {
            gridAppService.createGridCell(xpdGrid, addList, userId, xpdId);
        }

        // 检查是否切换了配置方式
        if (!Objects.equals(configType, bean.getConfigType())) {
            xpdGrid.setConfigType(bean.getConfigType());
            gridAppService.resetGrid(xpdGrid, userId);
            //gridMapper.insertOrUpdate(xpdGrid);
            // 重置宫格设置
            List<XpdGridDimCombPO>  allDimCombList = new ArrayList<>();
            allDimCombList.addAll(addList);
            allDimCombList.addAll(updateList);
            gridAppService.createGridCell(xpdGrid, allDimCombList, userId, xpdId);
            gridAppService.createGridLevel(xpdGrid, userId, 0, xpdId);
        } else {
            RvGrid4CreateChildCmd child = cmd.get_childs();
            updateLevelList = dealGridLevel(orgId, userId, gridId, child);
        }
        gridMapper.insertOrUpdate(xpdGrid);
        if (CollectionUtils.isNotEmpty(updateList)) {
            gridDimCombMapper.batchUpdate(updateList);
        }

        if (CollectionUtils.isNotEmpty(addList)) {
            gridDimCombMapper.insertBatch(addList);
        }
        if (CollectionUtils.isNotEmpty(deletedDimCombIds)) {
            gridDimCombMapper.deleteByGridIdAndDimCombId(orgId, gridId, deletedDimCombIds, userId);
            // 删除宫格cell
            gridCellMapper.deleteByGridIdAndDimCombIds(orgId, gridId, deletedDimCombIds, userId);
        }
        if (CollectionUtils.isNotEmpty(updateLevelList)) {
            gridLevelMapper.updateBatch(updateLevelList);
        }

    }


    private List<XpdGridLevelPO> dealGridLevel(
        String orgId, String userId, String gridId, RvGrid4CreateChildCmd child) {
        List<GridLevelVO> gridLevel = child.getGrid_level();
        List<XpdGridLevelPO> levelPOlist = gridLevelMapper.listByGridId(orgId, gridId);
        List<XpdGridLevelPO> updateLevelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(gridLevel) && CollectionUtils.isNotEmpty(levelPOlist)) {
            Map<Integer, XpdGridLevelPO> gridLevelMap =
                StreamUtil.list2map(levelPOlist, XpdGridLevelPO::getOrderIndex);

            for (GridLevelVO gridLevelCmd : gridLevel) {
                XpdGridLevelPO gridLevelPO = gridLevelMap.get(gridLevelCmd.getOrderindex());
                gridLevelPO.setLevelName(gridLevelCmd.getName());
                gridLevelPO.setThirdDimColor(gridLevelCmd.getThirdDimColor());
                EntityUtil.setUpdate(gridLevelPO, userId);
                updateLevelList.add(gridLevelPO);
            }
        }
        return updateLevelList;
    }

    public List<XpdGridCombListVO> getCombList(String gridId){
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(gridId);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");
        return getXpdGridCombListVOS(gridId, xpdGrid);
    }

    @NotNull
    private List<XpdGridCombListVO> getXpdGridCombListVOS(String gridId, XpdGridPO xpdGrid) {
        List<XpdGridDimCombPO> gridDimCombList =
            gridDimCombMapper.findTempByGridId(xpdGrid.getOrgId(), gridId);
        List<XpdGridCombListVO> resList = new ArrayList<>();
        if (CollectionUtils.isEmpty(gridDimCombList)) {
            return new ArrayList<>();
        }

        // 获取维度组合数据
        List<String> dimCombIds = gridDimCombList.stream().map(XpdGridDimCombPO::getDimCombId).toList();
        List<XpdDimCombPO> xpdDimCombPOS = dimCombMapper.selectByIds(dimCombIds);
        Map<String, XpdDimCombPO> dimCombMap = StreamUtil.list2map(xpdDimCombPOS, XpdDimCombPO::getId);
        List<String> xSdDimIds = new ArrayList<>(xpdDimCombPOS.stream().map(XpdDimCombPO::getXSdDimId).toList());
        List<String> ySdDimIds = xpdDimCombPOS.stream().map(XpdDimCombPO::getYSdDimId).toList();
        xSdDimIds.addAll(ySdDimIds);
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(xpdGrid.getOrgId(), xSdDimIds);
        Map<String, String> dimMap =
            StreamUtil.list2map(baseDimDetails, DimensionList4Get::getId, DimensionList4Get::getDmName);

        for (XpdGridDimCombPO gridDimCombPO : gridDimCombList) {
            XpdGridCombListVO res = new XpdGridCombListVO();
            BeanCopierUtil.copy(gridDimCombPO, res);
            String dimCombId = res.getDimCombId();
            XpdDimCombPO xpdDimCombPO = dimCombMap.get(dimCombId);
            if (xpdDimCombPO == null) {
                continue;
            }
            res.setCombName(xpdDimCombPO.getCombName());
            res.setDimCombId(dimCombId);
            res.setXSdDimId(xpdDimCombPO.getXSdDimId());
            res.setXSdDimName(dimMap.get(xpdDimCombPO.getXSdDimId()));
            res.setYSdDimId(xpdDimCombPO.getYSdDimId());
            res.setYSdDimName(dimMap.get(xpdDimCombPO.getYSdDimId()));
            resList.add(res);
        }
        return resList;
    }

    public List<XpdGridCombListVO> getPrjCombList(String xpdId){
        XpdPO xpdPO = xpdMapper.selectByPrimaryKey(xpdId);
        XpdGridPO xpdGrid = gridMapper.selectByXpdId(xpdPO.getOrgId(), xpdId);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");
        return getXpdGridCombListVOS(xpdGrid.getId(), xpdGrid);
    }

    public List<XpdGridCellListVO> getGridCellList( String gridId, String dimCombId){
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(gridId);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");
        if (xpdGrid.getConfigType() == 0) {
            dimCombId = StringUtils.EMPTY;
        }
        List<XpdGridCellListVO> resList = new ArrayList<>();
        List<XpdGridCellPO> xpdGridCellList =
            gridCellMapper.listByGridIdAndDimCombId(xpdGrid.getOrgId(), gridId, dimCombId);
        if (CollectionUtils.isEmpty(xpdGridCellList)) {
            return new ArrayList<>();
        }
        log.info("getGridCellList gridColor ={}", appProperties.getGridColorMap());
        for (XpdGridCellPO xpdGridCellPO : xpdGridCellList) {
            XpdGridCellListVO res = new XpdGridCellListVO();
            BeanCopierUtil.copy(xpdGridCellPO, res);
            if (StringUtils.isNotBlank(res.getCellColor())) {
                res.setTextColor(appProperties.getGridColorMap().get(res.getCellColor().replace("#", "")));
            }

            resList.add(res);
        }

        return resList;
    }

    public List<XpdGridCellListVO> getPrjGridCellList(String orgId,  String xpdId, String dimCombId){
        XpdGridPO xpdGrid = gridMapper.selectByXpdId(orgId, xpdId);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");
        String gridId = xpdGrid.getId();
        if (xpdGrid.getConfigType() == 0) {
            dimCombId = StringUtils.EMPTY;
        }
        List<XpdGridCellListVO> resList = new ArrayList<>();
        List<XpdGridCellPO> xpdGridCellList =
            gridCellMapper.listByGridIdAndDimCombId(xpdGrid.getOrgId(), gridId, dimCombId);
        if (CollectionUtils.isEmpty(xpdGridCellList)) {
            return new ArrayList<>();
        }
        for (XpdGridCellPO xpdGridCell : xpdGridCellList) {
            XpdGridCellListVO res = new XpdGridCellListVO();
            BeanCopierUtil.copy(xpdGridCell, res);
            resList.add(res);
        }
        return resList;
    }

    public PagingList<GridCell4Get> cellPageList(String orgId, SearchDTO bean, PageRequest page) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        Map<String, String> filterEq = search.getFilterEq();
        String xpdId = filterEq.get("xpdId");
        String dimCombId = filterEq.get("id");
        PagingList<GridCell4Get> resPage = new PagingList<>();
        Page<XpdGridCellPO> pageParam = ApiUtil.toPage(page);
        if (StringUtils.isBlank(dimCombId)) {
            PagingList<XpdGridCellPO> pagingList = BeanCopierUtil.toPagingList(pageParam);
            resPage.setPaging(pagingList.getPaging());
            resPage.setDatas(new ArrayList<>());
            return resPage;
        }


        if (StringUtils.isBlank(xpdId)) {
            PagingList<XpdGridCellPO> pagingList = BeanCopierUtil.toPagingList(pageParam);
            resPage.setDatas(new ArrayList<>());
            resPage.setPaging(pagingList.getPaging());
            return resPage;
        }
        XpdGridPO xpdGridPo = gridMapper.selectByXpdId(orgId, xpdId);
        Validate.isNotNull(xpdGridPo, ExceptionKeys.XPD_GRID_NOT_FOUND);
        if (xpdGridPo.getConfigType() == 0) {
            dimCombId = StringUtils.EMPTY;
        }
        IPage<XpdGridCellPO> cellPage = gridCellMapper.listPageByXpdId(pageParam, orgId, xpdId, dimCombId);
        PagingList<XpdGridCellPO> pagingList = BeanCopierUtil.toPagingList(cellPage);
        resPage.setPaging(pagingList.getPaging());
        List<GridCell4Get> resList = new ArrayList<>();
        for (XpdGridCellPO data : pagingList.getDatas()) {
            GridCell4Get  cell4Get = new GridCell4Get();
            cell4Get.setId(data.getId());
            cell4Get.setOrgId(orgId);
            cell4Get.setName(data.getCellName());
            cell4Get.setCellcolor(data.getCellColor());
            cell4Get.setCellindex(Long.valueOf(data.getCellIndex()));
            resList.add(cell4Get);
        }
        resPage.setDatas(resList);
        return resPage;
    }

    /**
     * 编辑宫格
     *
     * @param orgId
     * @param beans
     * @param userId
     */
    public void editGridCell(String orgId, List<XpdGridCellEditCmd> beans, String userId){
        if (CollectionUtils.isEmpty(beans)) {
            return;
        }

        List<String> cellIds = beans.stream().map(XpdGridCellEditCmd::getId).toList();
        String cellId = cellIds.get(0);
        XpdGridCellPO xpdGridCellPO = gridCellMapper.selectByPrimaryKey(cellId);
        if (xpdGridCellPO == null) {
            log.error("editGridCell cellids={}", cellIds);
            throw new ApiException(ExceptionKeys.XPD_GRID_CELL_NOT_FOUND);
        }

        String gridId = xpdGridCellPO.getGridId();
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(gridId);
        if (xpdGrid.getTemplate() == 0 && xpdGrid.getGridState() == 1) {
            throw new ApiException("apis.sptalentrv.xpd.grid.publish");
        }

        List<XpdGridCellPO> xpdGridCellList= gridCellMapper.listByIds(orgId, cellIds);
        if (CollectionUtils.isEmpty(xpdGridCellList)) {
            return;
        }
        XpdGridPO xpdGridPO = gridMapper.selectByPrimaryKey(gridId);
        if (xpdGridPO.getTemplate() == 0 && xpdGridPO.getGridState() == 1) {
            throw new ApiException("apis.sptalentrv.xpd.grid.publish");
        }
        Map<String, XpdGridCellPO> gridCellMap =
            StreamUtil.list2map(xpdGridCellList, XpdGridCellPO::getId);


        List<XpdGridCellPO> updateList = new ArrayList<>();
        for (XpdGridCellEditCmd bean : beans) {
            String id = bean.getId();
            XpdGridCellPO xpdGridCell = gridCellMap.get(id);
            if (xpdGridCell == null) {
                continue;
            }
            xpdGridCell.setCellName(bean.getCellName());
            xpdGridCell.setCellColor(bean.getCellColor());
            xpdGridCell.setCellDesc(bean.getCellDesc());
            EntityUtil.setUpdate(xpdGridCell, userId);
            updateList.add(xpdGridCell);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            gridCellMapper.updateBatch(updateList);
        }

    }


    public List<XpdGridLevelVO> getGridLevel(String orgId, String gridId){
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(gridId);
        Validate.isNotNull(xpdGrid, ExceptionKeys.XPD_GRID_NOT_FOUND);

        List<XpdGridLevelPO> list = gridLevelMapper.listByGridId(orgId, gridId);
        List<XpdGridLevelVO> resList = new ArrayList<>();
        for (XpdGridLevelPO xpdGridLevelPO : list) {
            XpdGridLevelVO res = new XpdGridLevelVO();
            //BeanCopierUtil.copy(xpdGridLevelPO, res);
            res.setGridLevelName(xpdGridLevelPO.getLevelName());
            res.setGridLevelId(xpdGridLevelPO.getId());
            res.setOrderIndex(xpdGridLevelPO.getOrderIndex());
            res.setLevelNameI18n(xpdGridLevelPO.getLevelNameI18n());
            res.setThirdDimColor(xpdGridLevelPO.decideThirdDimColor(xpdGrid, false, appProperties));
            resList.add(res);
        }
        return resList;
    }

    public List<XpdGridLevelVO> getPrjGridLevel(String orgId, String xpdId){
        List<XpdGridLevelPO> list = gridLevelMapper.listByXpdId(orgId, xpdId);
        List<XpdGridLevelVO> resList = new ArrayList<>();
        for (XpdGridLevelPO xpdGridLevelPO : list) {
            XpdGridLevelVO res = new XpdGridLevelVO();
            res.setGridLevelId(xpdGridLevelPO.getId());
            res.setGridLevelName(xpdGridLevelPO.getLevelName());
            res.setOrderIndex(xpdGridLevelPO.getOrderIndex());
            res.setLevelNameI18n(xpdGridLevelPO.getLevelNameI18n());
            resList.add(res);
        }
        return resList;
    }


    public void editGridLevel(String orgId, List<XpdGridLevelCmd> beans,  String userId, String gridId){
        if (CollectionUtils.isEmpty(beans)) {
            return;
        }

        List<XpdGridLevelPO> list = gridLevelMapper.listByGridId(orgId, gridId);
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        XpdGridPO xpdGridPO = gridMapper.selectByPrimaryKey(gridId);
        if (xpdGridPO.getTemplate() == 0 && xpdGridPO.getGridState() == 1) {
            throw new ApiException("apis.sptalentrv.xpd.grid.publish");
        }
        Map<String, XpdGridLevelPO> gridLevelMap =
            StreamUtil.list2map(list, XpdGridLevelPO::getId);
        List<XpdGridLevelPO> updateList = new ArrayList<>();
        for (XpdGridLevelCmd bean : beans) {
            XpdGridLevelPO gridLevelPO = gridLevelMap.get(bean.getId());
            if (gridLevelPO == null) {
                continue;
            }
            gridLevelPO.setLevelName(bean.getLevelName());
            EntityUtil.setUpdate(gridLevelPO, userId);
            updateList.add(gridLevelPO);
        }
        gridLevelMapper.updateBatch(updateList);
    }

    public List<XpdGridRatioVO> getGridRatioList(String id){
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(id);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");
        String gridId = xpdGrid.getId();
        Integer configType = xpdGrid.getConfigType();
        List<XpdGridDimCombDTO> gridCombList =
            gridDimCombMapper.findGridCombList(xpdGrid.getOrgId(), id);
        if (CollectionUtils.isEmpty(gridCombList)) {
            return new ArrayList<>();
        }
        List<XpdGridRatioVO> resList = new ArrayList<>();
        if (configType == 0) {
            XpdGridRatioVO res = new XpdGridRatioVO();
            res.setDimCombId("");
            resList.add(res);
        } else {
            for (XpdGridDimCombDTO xpdGridDimCombDTO : gridCombList) {
                XpdGridRatioVO res = new XpdGridRatioVO();
                BeanCopierUtil.copy(xpdGridDimCombDTO, res);
                resList.add(res);
            }
        }


        List<XpdGridRatioPO> xpdGridRatioList = gridRatioMapper.listByGridId(xpdGrid.getOrgId(), id);
        /*if (CollectionUtils.isEmpty(xpdGridRatioList)) {
            return resList;
        }*/
        List<String> cellIdsList = xpdGridRatioList.stream().map(XpdGridRatioPO::getGridCellIds).toList();
        List<String> cellIdAllList = new ArrayList<>();
        for (String cellIds : cellIdsList) {
            String[] cellIdList = cellIds.split(";");
            cellIdAllList.addAll(Lists.newArrayList(cellIdList));
        }
        List<XpdGridCellPO> xpdGridCellList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cellIdAllList)) {
            xpdGridCellList = gridCellMapper.listByIds(xpdGrid.getOrgId(), cellIdAllList);
        }


        Map<String, XpdGridCellPO> gridCellMap =
            StreamUtil.list2map(xpdGridCellList, XpdGridCellPO::getId);

        Map<String, List<XpdGridRatioPO>> gridRatioListMap =
            xpdGridRatioList.stream().collect(Collectors.groupingBy(XpdGridRatioPO::getDimCombId));

        for (XpdGridRatioVO xpdGridRatioVO : resList) {
            List<XpdGridCellListVO> gridCellList = getGridCellList(gridId, xpdGridRatioVO.getDimCombId());
            xpdGridRatioVO.setGridCellList(gridCellList);

            List<XpdGridRatioDetailVO> ratioDetailList  = new ArrayList<>();
            List<XpdGridRatioPO> gridRatioList =
                gridRatioListMap.get(xpdGridRatioVO.getDimCombId());
            if (CollectionUtils.isEmpty(gridRatioList)) {
                continue;
            }
            for (XpdGridRatioPO xpdGridRatioPO : gridRatioList) {
                XpdGridRatioDetailVO gridRatioDetailVO = new XpdGridRatioDetailVO();
                gridRatioDetailVO.setRatioId(xpdGridRatioPO.getId());
                gridRatioDetailVO.setRatio(xpdGridRatioPO.getRatio());

                List<XpdCellRatioVO> cellRatioList = new ArrayList<>();
                String gridCellIds = xpdGridRatioPO.getGridCellIds();
                String[] cellIds = gridCellIds.split(";");
                for (String cellId : cellIds) {
                    XpdGridCellPO xpdGridCell = gridCellMap.get(cellId);
                    if (xpdGridCell == null) {
                        continue;
                    }
                    XpdCellRatioVO cellRatioVO = new XpdCellRatioVO();
                    cellRatioVO.setCellId(xpdGridCell.getId());
                    cellRatioVO.setCellName(xpdGridCell.getCellName());
                    cellRatioList.add(cellRatioVO);
                }
                gridRatioDetailVO.setCellRatioList(cellRatioList);
                ratioDetailList.add(gridRatioDetailVO);
            }
            xpdGridRatioVO.setRatioDetailList(ratioDetailList);

        }
        return resList;
    }

    public void editGridRatio(List<XpdTempRatioCmd> beans, String id, String userId){
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(id);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");
        String orgId = xpdGrid.getOrgId();
        Integer configType = xpdGrid.getConfigType();
        // 0-模板,1-项目级
        Integer template = xpdGrid.getTemplate();
        String xpdId = "";
        if (template == 1) {
            xpdId = xpdGrid.getXpdId();
        } else {
            xpdId = AppConstants.TEMPLATE_XPD_ID;
        }

        List<XpdDimCombPO> xpdDimCombList = dimCombMapper.selectByOrgId(orgId);
        Set<String> dimCombIds =
            xpdDimCombList.stream().map(XpdDimCombPO::getId).collect(Collectors.toSet());

        List<XpdGridCellPO> xpdGridCellList = gridCellMapper.listByGridId(orgId, id);
        Map<String, XpdGridCellPO> gridCellMap =
            StreamUtil.list2map(xpdGridCellList, XpdGridCellPO::getId);

        Map<String, List<XpdGridCellPO>> combCellMap =
            xpdGridCellList.stream().collect(Collectors.groupingBy(XpdGridCellPO::getDimCombId));

        List<XpdGridRatioPO> gridRatioList = new ArrayList<>();
        for (XpdTempRatioCmd bean : beans) {
            String dimCombId = bean.getDimCombId();
            if (configType == 0) {
                dimCombId = StringUtils.EMPTY;
            } else {
                if (!dimCombIds.contains(bean.getDimCombId())) {
                    continue;
                }
            }

            if (CollectionUtils.isEmpty(bean.getRatioDetailList())){
                throw new ApiException("apis.sptalentrv.xpd.grid.cell.empty");
            }
            List<XpdGridCellPO> dataGridCellList = combCellMap.get(dimCombId);
            Map<String, String> cellMap =
                StreamUtil.list2map(dataGridCellList, XpdGridCellPO::getId,
                    XpdGridCellPO::getCellName);
            int index = 1;
            for (XpdCellRatioCmd xpdCellRatioCmd : bean.getRatioDetailList()) {
                if (CollectionUtils.isEmpty(xpdCellRatioCmd.getCellRatioList())) {
                    throw new ApiException("apis.sptalentrv.xpd.grid.cell.empty");
                }

                XpdGridRatioPO gridRatio = new XpdGridRatioPO();
                gridRatio.setId(ApiUtil.getUuid());
                gridRatio.setOrgId(orgId);
                gridRatio.setXpdId(xpdId);
                gridRatio.setGridId(id);
                gridRatio.setDimCombId(dimCombId);

                List<String> cellIds = xpdCellRatioCmd.getCellRatioList().stream().map(XpdCellRatioVO::getCellId).toList();
                String gridCellIds = String.join(";", cellIds);
                gridRatio.setGridCellIds(gridCellIds);
                List<String> cellIndex = new ArrayList<>();
                cellIds.forEach(x -> {
                    XpdGridCellPO gridCell = gridCellMap.get(x);
                    if (gridCell == null) {
                        return;
                    }
                    cellIndex.add(String.valueOf(gridCell.getCellIndex()));
                    cellMap.remove(x);
                });
                String cellIndexs = String.join(";", cellIndex);
                gridRatio.setGridCellIndex(cellIndexs);

                gridRatio.setRatio(xpdCellRatioCmd.getRatio());
                gridRatio.setOrderIndex(index++);
                EntityUtil.setAuditFields(gridRatio, userId);
                gridRatioList.add(gridRatio);
            }
            // 所以宫格都必须配置
            if (!cellMap.isEmpty()) {
                throw new ApiException("apis.sptalentrv.xpd.grid.cell.not.used");
            }

        }

        if (CollectionUtils.isNotEmpty(gridRatioList)) {
            // 删除旧的
            gridRatioMapper.deleteGridRatio(orgId, id, userId);

            gridRatioMapper.batchInsert(gridRatioList);
        }

    }

    /**
     * 发布
     *
     * @param id
     * @param userId
     */
    public void publishGridModel(String id, String userId){
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(id);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");

        xpdGrid.setGridState(1);
        EntityUtil.setUpdate(xpdGrid, userId);
        gridMapper.insertOrUpdate(xpdGrid);
    }

    public void deleteGridModel(String id, String userId){
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(id);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");
        if (xpdGrid.getSourceType() == 0) {
            throw new ApiException("apis.sptalentrv.xpd.grid.temp.in.delete");
        }
        if (xpdGrid.getTemplate() == 0 && xpdGrid.getGridState() == 1) {
            throw new ApiException("apis.sptalentrv.xpd.grid.temp.publish");
        }


        xpdGrid.setDeleted(1);
        EntityUtil.setUpdate(xpdGrid, userId);
        gridMapper.insertOrUpdate(xpdGrid);
    }
    public void withdrawGridModel(String id, String userId){
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(id);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");

        xpdGrid.setGridState(0);
        EntityUtil.setUpdate(xpdGrid, userId);
        gridMapper.insertOrUpdate(xpdGrid);
    }

    public PagingList<GridLevel4Get> pageGridLevel(String orgId, SearchDTO bean, PageRequest page){
        Page<XpdGridLevelPO> pageParam = ApiUtil.toPage(page);
        PagingList<GridLevel4Get> resPage = new PagingList<>();

        QueryUtil.Search search = QueryUtil.parse(bean);
        String xpdId = search.getEqVal("xpdId");
        if (xpdId == null) {
            PagingList<XpdGridLevelPO> gridPagingList = BeanCopierUtil.toPagingList(pageParam);
            resPage.setDatas(new ArrayList<>());
            resPage.setPaging(gridPagingList.getPaging());
            return resPage;
        }
        IPage<XpdGridLevelPO> ipagePO = gridLevelMapper.listByXpdIdPage(pageParam, orgId, xpdId);
        PagingList<XpdGridLevelPO> pagingList = BeanCopierUtil.toPagingList(ipagePO);

        List<GridLevel4Get> resList = new ArrayList<>();
        for (XpdGridLevelPO data : pagingList.getDatas()) {
            GridLevel4Get res = new GridLevel4Get();
            res.setRvGridId(data.getGridId());
            res.setId(data.getId());
            res.setName(data.getLevelName());
            resList.add(res);
        }
        resPage.setDatas(resList);
        resPage.setPaging(pagingList.getPaging());
        return resPage;
    }

    public RvGrid4Get gridDetail(SearchDTO bean, String id) {
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(id);
        RvGrid4Get res = new RvGrid4Get();
        if (xpdGrid == null) {
            return res;
        }

        res.setId(id);
        res.setName(xpdGrid.getGridName());
        res.setGridtype(String.valueOf(xpdGrid.getGridType()));
        res.setGriddesc(xpdGrid.getGridDesc());
        res.setConfigtype(String.valueOf(xpdGrid.getConfigType()));
        res.setSourcetype(String.valueOf(xpdGrid.getSourceType()));
        res.setGridstate(String.valueOf(xpdGrid.getGridState()));
        UdpLiteUserPO user = udpLiteUserMapper.selectById(xpdGrid.getCreateUserId());
        if (user != null) {
            res.setCreateUserId(ApassEntityUtils.createDrawer4UserSimple(user.getFullname(), user.getId(),
                user.getUsername(), user.getImgUrl()));
        }
        // 获取管理项目信息
        String xpdId = xpdGrid.getXpdId();
        if (xpdGrid.getTemplate() == 0) {
            return res;
        }
        XpdInfoDto xpdMsg = xpdMapper.findXpdMsg(xpdGrid.getOrgId(), xpdId);

        res.setProjectid(ApassEntityUtils.createAmSlDrawerIdName(xpdId, xpdMsg.getXpdName()));
        return res;
    }

    /**
     * 获取项目的宫格信息
     * @param orgId
     * @param xpdId
     * @return
     */
    public RvGrid4Get getXpdGridInfo(String orgId, String xpdId) {
        XpdGridPO xpdGrid = gridMapper.selectByXpdId(orgId, xpdId);
        Validate.isNotNull(xpdGrid, ExceptionKeys.XPD_GRID_NOT_FOUND);
        return getRvGrid4Get(xpdGrid);
    }

    @Nonnull
    private static RvGrid4Get getRvGrid4Get(XpdGridPO xpdGrid) {
        RvGrid4Get res = new RvGrid4Get();
        res.setId(xpdGrid.getId());
        res.setName(xpdGrid.getGridName());
        res.setGridtype(String.valueOf(xpdGrid.getGridType()));
        res.setGriddesc(xpdGrid.getGridDesc());
        res.setConfigtype(String.valueOf(xpdGrid.getConfigType()));
        res.setSourcetype(String.valueOf(xpdGrid.getSourceType()));
        res.setGridstate(String.valueOf(xpdGrid.getGridState()));
        return res;
    }

    public RvGrid4Get detailParentChild(String orgId, String grId) {
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(grId);
        Validate.isNotNull(xpdGrid, ExceptionKeys.XPD_GRID_NOT_FOUND);
        RvGrid4Get res = new RvGrid4Get();
        res.setId(xpdGrid.getId());
        res.setOrgId(orgId);
        res.setName(xpdGrid.getGridName());
        res.setGridtype(String.valueOf(xpdGrid.getGridType()));
        res.setConfigtype(String.valueOf(xpdGrid.getConfigType()));
        res.setGriddesc(xpdGrid.getGridDesc());

        // 维度组合
        RvGrid4CreateChildVO _childs = new RvGrid4CreateChildVO();
        List<GridCombCmd> grid_comb = new ArrayList<>();
        List<XpdGridDimCombDTO> gridCombList = gridDimCombMapper.findGridCombList(orgId, grId);
        if (CollectionUtils.isEmpty(gridCombList)) {
            return res;
        }
        List<String> dimIds = new ArrayList<>();
        gridCombList.forEach( x-> {
            dimIds.add(x.getXDimId());
            dimIds.add(x.getYDimId());
        });
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(orgId, dimIds);
        if (CollectionUtils.isEmpty(baseDimDetails)) {
            log.error("detailParentChild baseDimDetails orgId={}, dimIds={}", orgId, dimIds);
            return res;
        }
        Map<String, String> dimMap =
            StreamUtil.list2map(baseDimDetails, DimensionList4Get::getId, DimensionList4Get::getDmName);
        for (XpdGridDimCombDTO dimComb : gridCombList) {
            GridCombCmd gridComb = new GridCombCmd();
            gridComb.setShowtype(dimComb.getShowType());
            List<GridCombname> combnames = new ArrayList<>();
            GridCombname gridCombname = new GridCombname();
            gridCombname.setId(dimComb.getDimCombId());
            gridCombname.setName(dimComb.getDimCombName());

            gridCombname.setXsddimid(ApassEntityUtils.create4App(dimComb.getXDimId(), dimMap.get(dimComb.getXDimId())));
            //gridCombname.setXsddimid(dimMap.get(dimComb.getXDimId()));
            gridCombname.setYsddimid(ApassEntityUtils.create4App(dimComb.getYDimId(), dimMap.get(dimComb.getYDimId())));
            //gridCombname.setYsddimid(dimMap.get(dimComb.getYDimId()));
            combnames.add(gridCombname);
            gridComb.setCombname(combnames);
            gridComb.setXsddimid(dimMap.get(dimComb.getXDimId()));
            gridComb.setYsddimid(dimMap.get(dimComb.getYDimId()));
            grid_comb.add(gridComb);
        }
        _childs.setGrid_comb(grid_comb);
        // 维度分层
        List<XpdGridLevelPO> gridLevelList = gridLevelMapper.listByGridId(orgId, grId);
        if (CollectionUtils.isNotEmpty(gridLevelList)) {
            List<GridLevelVO> grid_level = new ArrayList<>();
            for (XpdGridLevelPO xpdGridLevel : gridLevelList) {
                GridLevelVO level = new GridLevelVO();
                level.setName(xpdGridLevel.getLevelName());
                level.setOrderindex(xpdGridLevel.getOrderIndex());
                level.setThirdDimColor(xpdGridLevel.decideThirdDimColor(xpdGrid, false,
                    appProperties));
                grid_level.add(level);
            }
            _childs.setGrid_level(grid_level);
        }
        res.set_childs(_childs);

        return res;
    }

    /**
     * 获取机构下指定项目中的所有宫格，如果传入的xpdId是模板id，则获取机构模板下的所有宫格
     *
     * @param orgId
     * @param xpdId
     * @param sourceType
     * @param gridType
     * @return
     */
    public XpdGridVO getXpdGrid(String orgId, String xpdId, int sourceType, int gridType) {
        List<XpdGridPO> xpdGrids = gridMapper.selectByXpdIdAndSourceType(orgId, xpdId, sourceType, gridType);
        if (CollectionUtils.isEmpty(xpdGrids)) {
            return null;
        }
        XpdGridVO xpdGridVo = XpdGridVO.Assembler.INSTANCE.toXpdGridVo(xpdGrids.iterator().next());
        // 填充分层信息
        List<XpdGridLevelPO> xpdGridLevels = xpdGridLevelMapper.listByGridId(orgId, xpdGridVo.getId());
        List<XpdGridLevelVO> xpdGridLevelVos = XpdGridLevelVO.Assembler.INSTANCE.toXpdGridLevelVos(xpdGridLevels);
        xpdGridVo.setGridLevelInfoList(xpdGridLevelVos);
        List<XpdDimCombInfoVO> xpdGridDimCombs = xpdDimCombAppService.getXpdGridDimCombs(orgId, xpdId, xpdGridVo.getId());
        xpdGridVo.setDimCombList(xpdGridDimCombs);
        return xpdGridVo;
    }

    public List<PrjUserDimConfVO> findDimensionHeader(String orgId, String projectId) {
        List<XpdDimPO> dimPOS = xpdDimMapper.listByXpdId(orgId, projectId);
        if (CollectionUtils.isEmpty(dimPOS)) {
            return new ArrayList<>();
        }
        List<String> dimIds = dimPOS.stream().map(XpdDimPO::getSdDimId).toList();
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(orgId, dimIds);
        List<PrjUserDimConfVO> resList = new ArrayList<>();
        for (DimensionList4Get baseDimDetail : baseDimDetails) {
            PrjUserDimConfVO res = new PrjUserDimConfVO();
            res.setDimensionName(baseDimDetail.getDmName());
            res.setDimensionId(baseDimDetail.getId());
            resList.add(res);
        }
        return resList;
    }

    public List<PrjUserDimConfVO> findDimensionHeader4Xpd(String orgId, String projectId) {
        List<XpdDimPO> dimList = xpdDimMapper.listByXpdId(orgId, projectId);
        if (CollectionUtils.isEmpty(dimList)) {
            return new ArrayList<>();
        }
        List<String> dimIds = dimList.stream().map(XpdDimPO::getSdDimId).toList();
        List<DimensionList4Get> baseDimDetail = spsdAclService.getBaseDimDetail(orgId, dimIds);
        Map<String, String> dimMap =
            StreamUtil.list2map(baseDimDetail, DimensionList4Get::getId, DimensionList4Get::getDmName);
        List<PrjUserDimConfVO> resList = new ArrayList<>();
        for (XpdDimPO xpdDimPO : dimList) {
            PrjUserDimConfVO res = new PrjUserDimConfVO();
            res.setDimensionId(xpdDimPO.getSdDimId());
            res.setDimensionName(dimMap.get(xpdDimPO.getSdDimId()));
            resList.add(res);
        }
        return resList;
    }

    public boolean checkGridRatioList(String orgId, String xpdId) {
        XpdGridPO xpdGrid = gridMapper.selectByXpdId(orgId, xpdId);
        if (Objects.isNull(xpdGrid)) {
            return false;
        }
        List<XpdGridRatioPO> xpdGridRatioPOS = gridRatioMapper.listByGridId(orgId, xpdGrid.getId());
        return CollectionUtils.isNotEmpty(xpdGridRatioPOS);
    }
}
