package com.yxt.talent.rv.application.prj.prj;

import com.google.common.collect.Maps;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.spevalfacade.bean.EvalClientAddUser;
import com.yxt.spevalfacade.bean.Evaluation4Get;
import com.yxt.spevalfacade.bean.EvaluationUser;
import com.yxt.spevalfacade.bean.EvaluationUser4Create;
import com.yxt.spevalfacade.bean.evaluation.BindEvaluaFacade;
import com.yxt.spevalfacade.bean.evaluation.Evaluation;
import com.yxt.spevalfacade.bean.evaluation.EvaluationUser4Get;
import com.yxt.spevalfacade.bean.evaluation.EvaluationUserRelationBean;
import com.yxt.spevalfacade.bean.standar.StandarReq;
import com.yxt.spevalfacade.config.EvaluationConfig4Get;
import com.yxt.talent.rv.application.prj.dim.dto.PrjDimConfSimpleDTO;
import com.yxt.talent.rv.application.prj.user.legacy.PrjUserAppService;
import com.yxt.talent.rv.controller.manage.prj.prj.viewobj.PrjConfigDimVO;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjCalcResultVO;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConfTool;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfToolMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfToolPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 校验class
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrjValidator {

    private final SpevalAclService spevalAclService;
    private final PrjUserMapper prjUserMapper;
    private final PrjDimConfToolMapper prjDimConfToolMapper;
    private final PrjUserAppService prjUserAppService;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final PrjDimConfMapper prjDimConfMapper;


    public void chkDeletedEval(String orgId, String prjId, PrjCalcResultVO res){
        List<PrjDimConfToolPO> dctList = prjDimConfToolMapper.selectByOrgIdAndPrjIdAndToolTypes(
                orgId, prjId,
                Arrays.asList(PrjDimConfTool.Type.EVAL.getCode(), PrjDimConfTool.Type.BZ.getCode()));

        if (CollectionUtils.isEmpty(dctList)) {
            return;
        }

        List<String> toolIds = dctList.stream()
                .map(PrjDimConfToolPO::getToolId)
                .collect(Collectors.toList());

        StandarReq req = new StandarReq();
        req.setOrgId(orgId);
        req.setEvalIds(toolIds);
        List<Evaluation> evaluations = spevalAclService.getBatchEvaluation(req);
        List<String> evalIds = evaluations.stream()
                .filter(x -> x.getDeleted() == 1)
                .map(Evaluation::getId).toList();

        Set<String> dimensionConfigIds = dctList.stream()
                .filter(x -> evalIds.contains(x.getToolId()))
                .map(PrjDimConfToolPO::getDimensionConfigId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(dimensionConfigIds)) {
            return;
        }
        // 获取维度名称
        List<PrjDimConfSimpleDTO> prjDimConfSimpleDTOS =
                prjDimConfMapper.listSimplePrjDimConf(orgId,
                        new ArrayList<>(dimensionConfigIds));

        if (CollectionUtils.isNotEmpty(prjDimConfSimpleDTOS)){
            List<PrjConfigDimVO> dimensionConfigs = new ArrayList<>();
            for (PrjDimConfSimpleDTO prjDimConfSimpleDTO : prjDimConfSimpleDTOS) {
                PrjConfigDimVO prjConfigDimVO = new PrjConfigDimVO();
                prjConfigDimVO.setDimensionConfigId(prjDimConfSimpleDTO.getConfigId());
                prjConfigDimVO.setDimensionName(prjDimConfSimpleDTO.getDimensionName());
                dimensionConfigs.add(prjConfigDimVO);
            }
            res.setDimensionConfigs(dimensionConfigs);
        }
    }

    /**
     * 校验项目是否可以发布 1、如果有测评方案且方案中需要设置评估关系，项目发布需要校验评估关系数据
     *
     * @param projectId
     */
    @SuppressWarnings("java:S3776")
    public List<String> checkProjectReadyToStart(String orgId, String userId, String projectId, @Nullable List<String> usedEvelIds) {
        // 可发布测评集合
        List<String> canPublishEvalIds = new ArrayList<>();
        // 查看盘点配置的维度信息
        List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.listActiveDimConfWithToolByPrjId(orgId, projectId);
        if (CollectionUtils.isNotEmpty(prjDimConfs)) {
            // 测评集合
            List<String> evalIds = prjDimConfs.stream()
                    .filter(e -> (e.getToolType() == PrjDimConfTool.Type.EVAL.getCode()
                            || e.getToolType() == PrjDimConfTool.Type.BZ.getCode())).map(PrjDimConfPO::getToolId)
                    .collect(Collectors.toList());
            // 盘点人员集合
            List<PrjUserPO> prjUsers = prjUserMapper.selectByOrgIdAndPrjIdOrderByUpdateTimeDesc(orgId, projectId);
            List<String> userIds = StreamUtil.mapList(prjUsers, PrjUserPO::getUserId);
            List<String> compUserIds;
            if (CollectionUtils.isNotEmpty(usedEvelIds)) {
                evalIds = usedEvelIds;
            }
            for (String evalId : evalIds) {
                try {
                    Evaluation4Get evaluation4Get = spevalAclService.getEvaluationDetailCopy(evalId, orgId, userId);
                    //过滤已结束测评不需要校验。引用测评中心的测评，存在测评中心已结束
                    if (Objects.isNull(evaluation4Get) || Integer.valueOf(4).equals(evaluation4Get.getStatus())) {
                        continue;
                    }
                    compUserIds = new ArrayList<>(userIds);
                    // 查询是否有他评
                    BindEvaluaFacade search = new BindEvaluaFacade();
                    search.setOrgId(orgId);
                    search.setEvaluationId(evalId);
                    EvaluationConfig4Get config4Get = spevalAclService.getEvaluationConfig(search);
                    // 这里应该是在检查他评是否有任何一个维度设置了评估关系
                    if (config4Get.getEnabledLeader() == 1 || config4Get.getEnabledEqual() == 1
                            || config4Get.getEnabledSubordinate() == 1 || CommonUtil.checkIsNotBlank4Json(
                            config4Get.getCustomJson())) {
                        List<EvaluationUser> evaluationUserList = evaluation4Get.getEvaluationUserList();
                        List<String> evalUserIds = StreamUtil.mapList(evaluationUserList, EvaluationUser::getUserId);
                        compUserIds.removeAll(evalUserIds);
                        // 如果存在用户没有设置评估关系，则报错提醒用户去设置
                        Validate.isTrue(compUserIds.isEmpty(), ExceptionKeys.PRJ_SHOULD_SET_EVAL_RELATION,
                                evaluation4Get.getName());
                        // 查询已有的测评人员
                        CommonList<EvaluationUser4Get> evaluation4UserResult = spevalAclService.findEvalUsers(evalId,
                                orgId);
                        List<EvaluationUser4Get> existEvalUserRelations = evaluation4UserResult.getDatas();
                        Map<String, EvaluationUser4Get> evalUserMap = StreamUtil.list2map(existEvalUserRelations,
                                EvaluationUser4Get::getUserId);
                        // 校验评估人是否设置全
                        userIds.forEach(prjUserId -> {
                            int success = 0;
                            EvaluationUser4Get user4Get = evalUserMap.get(prjUserId);
                            if (user4Get != null) {
                                // 上级
                                if (config4Get.getEnabledLeader() == 1 && user4Get.getLeaderRelation() != null
                                        && CollectionUtils.isNotEmpty(user4Get.getLeaderRelation().getEvaluators())) {
                                    success++;
                                }
                                // 平级
                                if (config4Get.getEnabledEqual() == 1 && user4Get.getEqualRelation() != null
                                        && CollectionUtils.isNotEmpty(user4Get.getEqualRelation().getEvaluators())) {
                                    success++;
                                }
                                // 下级
                                if (config4Get.getEnabledSubordinate() == 1 && user4Get.getSubordinateRelation() != null
                                        && CollectionUtils.isNotEmpty(
                                        user4Get.getSubordinateRelation().getEvaluators())) {
                                    success++;
                                }
                                //自定义维度判断
                                if (CollectionUtils.isNotEmpty(user4Get.getCustomRelation())) {
                                    for (EvaluationUserRelationBean relationBean : user4Get.getCustomRelation()) {
                                        if (null != relationBean.getEvaluators() && CollectionUtils.isNotEmpty(
                                                relationBean.getEvaluators())) {
                                            success++;
                                        }
                                    }
                                }

                                Validate.isTrue(success > 0, ExceptionKeys.PRJ_SHOULD_SET_EVAL_RELATION,
                                        evaluation4Get.getName());
                            }
                        });
                    }
                    // 已发布不需要处理
                    if (!(evaluation4Get.getStatus() == 3 || evaluation4Get.getStatus() == 4)) {
                        canPublishEvalIds.add(evalId);
                    }
                } catch (ApiException e) {
                    throw new ApiException(e.getErrorKey(), e.getValues());
                } catch (Exception e) {
                    log.error("LOG62800:PrjUserService-checkEvalUser error, evalId:{}", evalId, e);
                    throw new ApiException("apis.sptalentrv.prj.eval.user.check.error", e.getMessage());
                }
            }
        }
        return canPublishEvalIds;
    }

    /**
     * 项目结束将测评结束
     *
     * @param prj
     */
    public void checkProjectReadyToFinish(UserCacheDetail userCache, String token, PrjPO prj) {
        // 查看盘点配置的维度信息
        String orgId = userCache.getOrgId();
        List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.listActiveDimConfWithToolByPrjId(orgId, prj.getId());
        if (CollectionUtils.isNotEmpty(prjDimConfs)) {
            List<String> evalIds = prjDimConfs.stream().filter(e -> e.getToolType() == 2).map(PrjDimConfPO::getToolId)
                    .collect(Collectors.toList());
            evalIds.forEach(evalId -> finishEval(evalId, prj.getOrgId(), userCache.getUserId(), token));
        }
    }

    /**
     * 结束测评
     *
     * @param evalId 测评id
     * @param orgId  机构id
     * @param userId 用户id
     */
    public void finishEval(String evalId, String orgId, String userId, String token) {
        try {
            log.debug("LOG63290:request to close evalution,evalId={}", evalId);
            spevalAclService.finishEvaluation(evalId, orgId, userId, token);
        } catch (ApiException e) {
            log.error("LOG62810:close evaluation error={}", e.getMessage());
            throw new ApiException(e.getErrorKey(), e.getMessage());
        }
    }

    public void checkProjectReadyToPublish(UserCacheDetail userCache, String projectId, String token) {
        List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.selectByOrgIdAndPrjId(userCache.getOrgId(), projectId);
        // 是否有未配置维度
        Validate.isNotEmpty(prjDimConfs, ExceptionKeys.PRJ_DIM_CONF_NOT_EXISTS);
        List<PrjDimConfPO> noConfig = prjDimConfs.stream().filter(e -> e.getDimensionStatus() == 0)
                .collect(Collectors.toList());
        Validate.isTrue(CollectionUtils.isEmpty(noConfig), ExceptionKeys.PRJ_DIM_NOT_CONFIG);
        // 校验测评是否含有自评，如果是收费问卷，校验额度是否满足
        checkEvalLimitWhenPublish(userCache, projectId, prjDimConfs, token);
    }

    /**
     * 校验测评是否含有自评，如果是收费问卷，校验额度是否满足 额度满足，需要往测评里面加人
     */
    private void checkEvalLimitWhenPublish(UserCacheDetail userCache, String projectId, List<PrjDimConfPO> prjDimConfs,
            String token) {
        String orgId = userCache.getOrgId();
        List<String> configIds = prjDimConfs.stream().map(PrjDimConfPO::getId).collect(Collectors.toList());
        List<PrjDimConfToolPO> configTools = prjDimConfToolMapper.selectByOrgIdAndDimConfIdsOrderByCreateTimeDesc(orgId,
                configIds);
        configTools = configTools.stream()
                .filter(configTool -> configTool.getToolType() == PrjDimConfTool.Type.EVAL.getCode()
                        || configTool.getToolType() == PrjDimConfTool.Type.BZ.getCode()).collect(Collectors.toList());
        List<String> evalIds = configTools.stream().map(PrjDimConfToolPO::getToolId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(evalIds)) {
            return;
        }
        // 盘点人员集合
        List<PrjUserPO> prjUsers = prjUserMapper.selectByOrgIdAndPrjIdOrderByUpdateTimeDesc(orgId, projectId);
        List<String> userIds = StreamUtil.mapList(prjUsers, PrjUserPO::getUserId);
        // 所有人员udp信息
        List<UdpLiteUserPO> originalUdpLiteUsers = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        // 需要往测评加人的人员信息
        Map<String, List<String>> willAddEvalUserMap = new HashMap<>(8);
        // 额度不足的测评问卷
        List<String> evalLimitOverflow = new ArrayList<>();
        Map<String, String> evalModelMap = Maps.newHashMap();
        for (String evalId : evalIds) {
            // 测评配置
            BindEvaluaFacade search = new BindEvaluaFacade();
            search.setOrgId(orgId);
            search.setEvaluationId(evalId);
            EvaluationConfig4Get config4Get = spevalAclService.getEvaluationConfig(search);
            // 他评就不处理？？？
            if (config4Get.getEnabledLeader() == 1 || config4Get.getEnabledEqual() == 1
                    || config4Get.getEnabledSubordinate() == 1 || CommonUtil.checkIsNotBlank4Json(
                    config4Get.getCustomJson())) {
                continue;
            }
            // 测评详情
            Evaluation4Get evaluation4Get = spevalAclService.getEvaluationDetail(evalId, orgId, userCache.getUserId());
            // 免费问卷，项目所有人都可加到测评中
            if (evaluation4Get.getCharged() == 0) {
                willAddEvalUserMap.put(evalId, userIds);
            } else {
                // 判断未加入测评的人数，问卷额度是否满足，不满足则直接报错
                List<String> evalUserIds = StreamUtil.mapList(evaluation4Get.getEvaluationUserList(),
                        EvaluationUser::getUserId);
                // 问卷剩余额度
                int marginCount;
                if (evaluation4Get.getStatus() == 1) {
                    marginCount = evaluation4Get.getMarginCount() - evalUserIds.size();
                } else {
                    marginCount = evaluation4Get.getMarginCount();
                }
                // 待加入测评人
                List<String> totalUserIds = new ArrayList<>(userIds);
                totalUserIds.removeAll(evalUserIds);
                // 如果待加入人数大于额度，直接返回报错
                if (marginCount - totalUserIds.size() < 0) {
                    evalLimitOverflow.add(evaluation4Get.getName());
                }
                willAddEvalUserMap.put(evalId, totalUserIds);
            }
        }
        // 如果有额度不足的测评问卷，报错
        Validate.isTrue(CollectionUtils.isEmpty(evalLimitOverflow), ExceptionKeys.PRJ_CHARGE_EVAL_OVERFLOW,
                evalLimitOverflow.toArray());

        // 往测评加人
        willAddEvalUserMap.forEach((evalId, waitAddUser) -> {
            // 满足额度加人
            List<UdpLiteUserPO> rvUdpUsers = originalUdpLiteUsers.stream().filter(e -> waitAddUser.contains(e.getId()))
                    .collect(Collectors.toList());
            CommonList<EvaluationUser4Create> beans = prjUserAppService.setEvaluationUser(rvUdpUsers,
                    evalModelMap.get(evalId), evalId, orgId);
            EvalClientAddUser evalClientAddUser = new EvalClientAddUser();
            evalClientAddUser.setBeans(beans);
            evalClientAddUser.setDomain(userCache.getDomain());
            evalClientAddUser.setPv("v2");
            evalClientAddUser.setToken(token);
            spevalAclService.addEvalUsersNew(evalId, orgId, userCache.getUserId(), evalClientAddUser);
        });
    }
}
