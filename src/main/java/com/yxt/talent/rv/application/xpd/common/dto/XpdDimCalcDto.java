package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/20 15:06
 */
@Data
@Schema(description = "维度计算规则")
public class XpdDimCalcDto {

    @Schema(description = "标准的ID[维度/指标]")
    private String sdDimId;

    @Schema(description = "权重[维度/指标]")
    private BigDecimal weight;

    @Schema(description = "分值[维度/指标], 如果设置了分制（5分制、10分制）, 则不需要默认就是分制代表的最大值，无需再通过指标或子维度计算总分")
    private BigDecimal totalScore;

    private List<XpdDimCalcDto> children;
}
