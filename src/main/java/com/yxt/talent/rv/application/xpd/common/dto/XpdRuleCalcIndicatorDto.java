package com.yxt.talent.rv.application.xpd.common.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: geyan
 * @Date: 13/12/24 10:59
 * @Description:
 **/
@Data
public class XpdRuleCalcIndicatorDto implements XpdIndicatorCalcBase {
    private String id;
    /**
     * 项目规则ID(指向rv_xpd_rule.id)
     */
    private String xpdRuleId;

    /**
     * 对应人才标准里的模型指标关系表id
     */
    private String sdIndicatorId;

    /**
     * 计算逻辑:0-无 1-求平均 2-求和 3-全部来源中达标 4-任一来源中达标
     */
    private Integer calcMethod;

    /**
     * 权重
     */
    private BigDecimal weight;

    private List<XpdDimRuleCalcRefDto> refList;
}
