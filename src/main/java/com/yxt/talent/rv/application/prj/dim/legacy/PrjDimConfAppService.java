package com.yxt.talent.rv.application.prj.dim.legacy;

import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.controller.manage.prj.dim.command.DimSameModelAddCmd;
import com.yxt.talent.rv.controller.manage.prj.dim.command.DimToolTypeAddCmd;
import com.yxt.talent.rv.controller.manage.prj.dim.command.PrjDimConfCreateCmd;
import com.yxt.talent.rv.controller.manage.prj.dim.command.PrjDimConfModelIdUpdateCmd;
import com.yxt.talent.rv.controller.manage.prj.prj.viewobj.PrjToolVO;
import com.yxt.talent.rv.domain.prj.Prj;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConf;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConfTool;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfToolMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimSameModelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleCondMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfToolPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimSameModelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRuleCondPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRulePO;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConf.DimStatus.NOT_CONFIGURE;
import static com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConfTool.Source.SAME_MODEL;
import static com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConfTool.Source.SELF_CREATE;
import static com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConfTool.Type.PERF;
import static com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConfTool.Type.UNKNOWN;

@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class PrjDimConfAppService {
    private static final int COUNT = 2;
    private final PrjDimMapper prjDimMapper;
    private final PrjDimRuleMapper prjDimRuleMapper;
    private final PrjDimRuleCondMapper prjDimRuleCondMapper;
    private final PrjDimConfToolMapper prjDimConfToolMapper;
    private final PrjDimSameModelMapper prjDimSameModelMapper;
    private final PrjMapper prjMapper;
    private final PrjDimConfMapper prjDimConfMapper;

    /**
     * 创建盘点下面下维度模型
     *
     * @param projectId       项目id
     * @param orgId           机构id
     * @param userId          用户id
     * @param prjDimCreateCmd 维度模型信息
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void createPrjDims(
            String projectId, String orgId, String userId,
            List<PrjDimConfCreateCmd> prjDimCreateCmd) {
        // 需要保存的数据
        List<PrjDimConfCreateCmd> saveData = prjDimCreateCmd.stream()
                .filter(projectDimension4Create ->
                        StringUtils.isBlank(projectDimension4Create.getDimensionConfigId()) &&
                        projectDimension4Create.getDimensionEnable() == 1)
                .collect(Collectors.toList());
        // 保存盘点对应维度
        List<PrjDimConfPO> savePrjDimConfs = new ArrayList<>();
        List<String> dimIds = new ArrayList<>();
        saveData.forEach(dim4Create -> {
            PrjDimConfPO prjDimConf = new PrjDimConfPO();
            if (StringUtils.isNotBlank(dim4Create.getDimensionId())) {
                prjDimConf.setDimensionId(dim4Create.getDimensionId());
            }
            if (StringUtils.isNotBlank(dim4Create.getSkillModelId())) {
                prjDimConf.setModelId(dim4Create.getSkillModelId());
            }
            prjDimConf.setOrgId(orgId);
            prjDimConf.setProjectId(projectId);
            prjDimConf.setDimensionStatus(NOT_CONFIGURE.getCode());
            prjDimConf.setOrderIndex(dim4Create.getOrderIndex());
            EntityUtil.setAuditFields(prjDimConf, userId);
            savePrjDimConfs.add(prjDimConf);
            dimIds.add(dim4Create.getDimensionId());
        });
        checkDimension(dimIds, orgId);
        if (CollectionUtils.isNotEmpty(savePrjDimConfs)) {
            prjDimConfMapper.insertOrUpdateBatch(savePrjDimConfs);
        }
        // 需要更新的数据
        updateData(orgId, userId, prjDimCreateCmd);
    }

    private void checkDimension(List<String> dimIds, String orgId) {
        if (CollectionUtils.isEmpty(dimIds)) {
            return;
        }
        List<PrjDimPO> prjDims =
                prjDimMapper.selectByOrgIdAndIfDimIdsOrderByCreateTime(orgId, dimIds);
        if (prjDims.size() < dimIds.size()) {
            throw new ApiException("apis.sptalentrv.prj.dim.deleted");
        }
        List<PrjDimPO> enable = prjDims.stream().filter(dim -> dim.getDimensionEnable() == 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(enable)) {
            throw new ApiException("apis.sptalentrv.prj.dim.enable");
        }
    }

    private void updateData(
            String orgId, String userId, List<PrjDimConfCreateCmd> prjDimCreateCmd) {
        // 获取需要更新的启用的维度
        List<PrjDimConfCreateCmd> updateData = prjDimCreateCmd.stream()
                .filter(projectDimension4Create ->
                        StringUtils.isNotBlank(projectDimension4Create.getDimensionConfigId()) &&
                        projectDimension4Create.getDimensionEnable() == 1)
                .collect(Collectors.toList());

        List<String> configIds = updateData.stream().map(PrjDimConfCreateCmd::getDimensionConfigId)
                .collect(Collectors.toList());
        Map<String, PrjDimConfCreateCmd> map =
                StreamUtil.list2map(updateData, PrjDimConfCreateCmd::getDimensionConfigId);
        List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.selectByOrgIdAndIds(orgId, configIds);
        List<String> deleteRules = new ArrayList<>();
        List<String> dimIds = new ArrayList<>();
        List<PrjDimConfPO> dimModelChange = new ArrayList<>();
        prjDimConfs.forEach(dimConf -> {
            PrjDimConfCreateCmd projectDimension4Create = map.get(dimConf.getId());
            dimConf.setDimensionId(projectDimension4Create.getDimensionId());
            dimConf.setOrderIndex(projectDimension4Create.getOrderIndex());
            if (dimConf.getToolType() != PERF.getCode() &&
                !dimConf.getModelId().equals(projectDimension4Create.getSkillModelId())) {
                deleteRules.add(dimConf.getId());
                dimConf.setDimensionStatus(NOT_CONFIGURE.getCode());
                dimConf.setToolId("");
                dimConf.setToolType(UNKNOWN.getCode());

                dimModelChange.add(dimConf);
            }
            dimIds.add(dimConf.getDimensionId());
            dimConf.setModelId(projectDimension4Create.getSkillModelId());
            EntityUtil.setUpdate(dimConf, userId);
        });

        checkDimension(dimIds, orgId);
        prjDimConfMapper.insertOrUpdateBatch(prjDimConfs);

        deleteRules.forEach(dimConfId -> deleteRule(orgId, dimConfId));

        // 获取需要删除的维度
        List<PrjDimConfCreateCmd> deleteData = prjDimCreateCmd.stream()
                .filter(projectDimension4Create ->
                        StringUtils.isNotBlank(projectDimension4Create.getDimensionConfigId()) &&
                        projectDimension4Create.getDimensionEnable() == 0)
                .collect(Collectors.toList());
        List<String> ids = deleteData.stream().map(PrjDimConfCreateCmd::getDimensionConfigId)
                .collect(Collectors.toList());
        prjDimConfMapper.deleteBatch(orgId, ids);
        dimModelChange.forEach(dimConf -> dimModelChange(dimConf, userId));
    }

    /**
     * 维度能力模型变更 工具清空 5697a2d3-0fa4-467f-97ee-dd06a0442c5a
     */
    private void dimModelChange(PrjDimConfPO prjDimConf, String userId) {
        String orgId = prjDimConf.getOrgId();
        String prjDimConfId = prjDimConf.getId();
        List<PrjDimConfToolPO> prjDimConfTools =
                prjDimConfToolMapper.selectByOrgIdAndDimConfId(orgId, prjDimConfId);
        List<PrjDimConfToolPO> selfTools = prjDimConfTools.stream()
                .filter(dimConfTool -> dimConfTool.getToolSource() == SELF_CREATE.getCode())
                .collect(Collectors.toList());

        List<PrjDimConfToolPO> sameModelTools = prjDimConfTools.stream()
                .filter(dimConfTool -> dimConfTool.getToolSource() == SAME_MODEL.getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(selfTools)) {
            List<String> ids =
                    selfTools.stream().map(PrjDimConfToolPO::getId).collect(Collectors.toList());
            prjDimConfToolMapper.deleteBatch(orgId, ids);
            // 判断 是否被其他维度同模使用
            List<String> toolIds = selfTools.stream().map(PrjDimConfToolPO::getToolId)
                    .collect(Collectors.toList());
            List<PrjDimConfToolPO> refTools =
                    prjDimConfToolMapper.selectSameModelByOrgIdAndPrjIdAndToolIds(orgId,
                            prjDimConf.getProjectId(), toolIds);

            // 删除同模的工具
            List<String> refToolsIds =
                    refTools.stream().map(PrjDimConfToolPO::getId).collect(Collectors.toList());
            prjDimConfToolMapper.deleteBatch(orgId, refToolsIds);

            // 引用同模测评维度配置id
            List<String> configIds =
                    refTools.stream().map(PrjDimConfToolPO::getDimensionConfigId).distinct()
                            .collect(Collectors.toList());
            List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.selectByOrgIdAndIds(orgId, configIds);

            // 改成引用同模的测评改成未配置
            prjDimConfs.forEach(config -> {
                EntityUtil.setUpdate(config, userId);
                config.setDimensionStatus(NOT_CONFIGURE.getCode());
            });
            prjDimConfMapper.insertOrUpdateBatch(prjDimConfs);

            // 删除规则
            prjDimConfs.forEach(dimConfId -> deleteRule(dimConfId.getOrgId(), dimConfId.getId()));

            // 删除同模数据
            List<PrjDimSameModelPO> modelSames =
                    prjDimSameModelMapper.selectByPrjIdAndEvalIds(orgId, prjDimConf.getProjectId(),
                            toolIds);
            List<String> modelSameIds =
                    modelSames.stream().map(PrjDimSameModelPO::getId).collect(Collectors.toList());
            prjDimSameModelMapper.deleteBatch(orgId, modelSameIds);
        }
        if (CollectionUtils.isNotEmpty(sameModelTools)) {
            List<String> ids = sameModelTools.stream().map(PrjDimConfToolPO::getId)
                    .collect(Collectors.toList());
            prjDimConfToolMapper.deleteBatch(orgId, ids);
        }
    }

    /**
     * 删除配置规则
     *
     * @param orgId     机构id
     * @param dimConfId 维度配置id
     */
    public void deleteRule(String orgId, String dimConfId) {
        // 删除配置规则
        PrjDimRulePO prjRule = prjDimRuleMapper.selectByDimConfId(orgId, dimConfId);
        if (prjRule != null) {
            prjDimRuleMapper.deleteBatch(orgId, List.of(prjRule.getId()));
            // 是否删除PrjDimRuleExpression
            List<PrjDimRuleCondPO> existPrjDimRuleConds =
                    prjDimRuleCondMapper.selectByOrgIdAndRuleId(orgId, prjRule.getId());
            if (CollectionUtils.isNotEmpty(existPrjDimRuleConds)) {
                List<String> ids = existPrjDimRuleConds.stream().map(PrjDimRuleCondPO::getId)
                        .collect(Collectors.toList());
                prjDimRuleCondMapper.deleteBatch(orgId, ids);
            }
        }
    }

    /**
     * 进行下一步时判断是否存在2个
     *
     * @param projectId 盘点项目id
     */
    public void checkProjectDimensionCount(String projectId, String orgId) {
        List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.selectByOrgIdAndPrjId(
                orgId, projectId);
        if (prjDimConfs.size() < COUNT) {
            throw new ApiException("apis.sptalentrv.prj.dim.config.count");
        }
    }

    /**
     * 绑定盘点工具类型
     *
     * @param dimToolTypeAddCmd 盘点工具类型
     * @param userId            用户id
     */
    public PrjDimConfPO bindTool(
            DimToolTypeAddCmd dimToolTypeAddCmd, String userId, String orgId) {
        PrjDimConfPO prjDimConf =
                prjDimConfMapper.selectByOrgIdAndId(orgId, dimToolTypeAddCmd.getDimensionConfigId()
                );
        if (prjDimConf == null) {
            throw new ApiException(ExceptionKeys.PRJ_DIM_CONF_NOT_EXISTS);
        }
        prjDimConf.setToolId("");
        prjDimConf.setToolType(dimToolTypeAddCmd.getRvType());
        if (dimToolTypeAddCmd.getRvType() == PrjDimConfTool.Type.IMPT.getCode()) {
            prjDimConf.setDimensionStatus(PrjDimConf.DimStatus.CONFIGURED.getCode());
        } else {
            prjDimConf.setDimensionStatus(NOT_CONFIGURE.getCode());
        }

        EntityUtil.setUpdate(prjDimConf, userId);
        prjDimConfMapper.insertOrUpdate(prjDimConf);
        deleteRule(prjDimConf.getOrgId(), prjDimConf.getId());
        return prjDimConf;
    }

    public PrjDimConfPO updateDimStatus(
            String orgId, String id, String operator, int rvType, int dimStatus) {
        PrjDimConfPO prjDimConf = prjDimConfMapper.selectByOrgIdAndId(orgId, id);
        Validate.isNotNull(prjDimConf, "");
        assert prjDimConf != null;
        prjDimConf.setDimensionStatus(dimStatus);
        prjDimConf.setToolType(rvType);
        EntityUtil.setUpdate(prjDimConf, operator);
        prjDimConfMapper.insertOrUpdate(prjDimConf);
        return prjDimConf;
    }

    public List<PrjDimConfPO> getDimByPrjId(
            String projectId, String orgId, @Nullable Integer status) {
        return prjDimConfMapper.selectByPrjIdAndOrgIdAndIfStatus(projectId, orgId, status);
    }

    public List<PrjDimConfPO> getDimensionByIds(
            List<String> ids, String orgId, List<Integer> types) {
        return prjDimConfMapper.selectByOrgIdAndToolTypesAndIdsAndDimStatusEq1(ids, orgId, types);
    }

    public void moveToolId(String orgId, String dimConfId, String userId) {
        PrjDimConfPO prjDimConf = prjDimConfMapper.selectByOrgIdAndId(orgId, dimConfId);
        if (prjDimConf == null) {
            throw new ApiException(ExceptionKeys.PRJ_DIM_CONF_NOT_EXISTS);
        }
        prjDimConf.setToolId("");
        prjDimConf.setDimensionStatus(NOT_CONFIGURE.getCode());
        EntityUtil.setUpdate(prjDimConf, userId);
        prjDimConfMapper.insertOrUpdate(prjDimConf);
        deleteRule(prjDimConf.getOrgId(), prjDimConf.getId());
    }

    /**
     * 查询盘点工具类型
     *
     * @param orgId
     * @param projectId
     */
    public List<PrjToolVO> findPrjTool(String orgId, String projectId) {
        List<String> toolTypes = prjDimConfMapper.listToolTypeByOrgIdAndPrjId(orgId, projectId);
        List<PrjToolVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(toolTypes)) {
            toolTypes.stream().sorted().forEach(toolType -> {
                PrjToolVO prjToolVO = new PrjToolVO();
                prjToolVO.setToolType(toolType);
                if (StringUtils.equals(toolType, "1")) {
                    prjToolVO.setToolName("绩效管理");
                } else if (StringUtils.equals(toolType, "2")) {
                    prjToolVO.setToolName("测评");
                } else if (StringUtils.equals(toolType, "3")) {
                    prjToolVO.setToolName("盘点数据导入");
                } else if (StringUtils.equals(toolType, "4")) {
                    prjToolVO.setToolName("倍智测评");
                }
                result.add(prjToolVO);
            });
        }
        return result;
    }

    /**
     * 绑定同模测评盘点工具
     *
     * @param dimSameModelAddCmd 盘点工具类型
     * @param userId             用户id
     */
    public PrjDimConfPO sameModelBind(
            DimSameModelAddCmd dimSameModelAddCmd, String userId, String orgId) {
        PrjDimConfPO prjDimConf =
                prjDimConfMapper.selectByOrgIdAndId(orgId, dimSameModelAddCmd.getDimensionConfigId()
                );
        if (prjDimConf == null) {
            throw new ApiException(ExceptionKeys.PRJ_DIM_CONF_NOT_EXISTS);
        }
        prjDimConf.setToolType(dimSameModelAddCmd.getRvType());
        prjDimConf.setDimensionStatus(NOT_CONFIGURE.getCode());

        EntityUtil.setUpdate(prjDimConf, userId);
        prjDimConfMapper.insertOrUpdate(prjDimConf);
        deleteRule(prjDimConf.getOrgId(), prjDimConf.getId());
        return prjDimConf;
    }

    /**
     * 清空模型所选维度
     *
     * @param userId    用户id
     * @param orgId     机构i的
     * @param dimConfId 盘点维度配置id
     */
    public void deleteSkillModel(String userId, String orgId, String dimConfId) {
        PrjDimConfPO prjDimConf = prjDimConfMapper.selectByOrgIdAndId(orgId, dimConfId);
        if (prjDimConf == null) {
            throw new ApiException(ExceptionKeys.PRJ_DIM_CONF_NOT_EXISTS);
        }
        PrjPO prj = prjMapper.selectByOrgIdAndId(orgId, prjDimConf.getProjectId());
        if (prj == null) {
            throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
        }
        // 盘点状态-未发布，未开始
        if (prj.getProjectStatus() == Prj.Status.DRAFT.getCode() ||
            prj.getProjectStatus() == Prj.Status.NOT_START.getCode()) {
            prjDimConf.setModelId("");
            prjDimConf.setDimensionStatus(NOT_CONFIGURE.getCode());
            EntityUtil.setUpdate(prjDimConf, userId);
            prjDimConfMapper.insertOrUpdate(prjDimConf);
            // 删除维度规则
            deleteRule(orgId, dimConfId);
            // 判断是否存在同模测评，存在一起进行删除逻辑
            dimModelChange(prjDimConf, userId);
        } else {
            throw new ApiException("apis.sptalentrv.prj.status.invalid");
        }
    }

    public void updateDimensionModelId(
            String orgId, String userId, PrjDimConfModelIdUpdateCmd modelIdUpdate) {
        log.debug("LOG63170:updateDimensionModelId bean=[{}]",
                BeanHelper.bean2Json(modelIdUpdate, ALWAYS));
        prjDimConfMapper
                .updateDimModelId(orgId, modelIdUpdate.getDimensionConfigId(),
                        modelIdUpdate.getNewModelId(), userId);
    }

}
