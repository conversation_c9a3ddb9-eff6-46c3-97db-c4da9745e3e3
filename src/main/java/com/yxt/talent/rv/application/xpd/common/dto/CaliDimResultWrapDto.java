package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CaliDimResultWrapDto {
    private String xpdLevelId;
    @Schema(description = "分值, 包括绩效得分")
    private BigDecimal scoreValue;
    @Schema(description = "达标率")
    private BigDecimal qualifiedPtg;
    private List<CaliDimResultDto> dimResults;
}
