package com.yxt.talent.rv.application.sys;

import com.yxt.spsdk.logsave.LogSaveService;
import com.yxt.talent.rv.infrastructure.trigger.task.xxljob.sys.SysLogNoticeTaskEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SysEventListener {

    private final LogSaveService logSaveService;

    @org.springframework.context.event.EventListener
    public void handleSysLogNoticeEvent(SysLogNoticeTaskEvent event) {
        log.info("LOG14125:{}", event);
        logSaveService.dingNotice();
    }

}
