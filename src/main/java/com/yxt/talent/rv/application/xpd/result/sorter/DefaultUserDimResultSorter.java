package com.yxt.talent.rv.application.xpd.result.sorter;

import com.yxt.talent.rv.application.xpd.common.enums.XpdImportTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultsVO;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimCalcTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimResultTypeEnum;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfResultConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdImportMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRulePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 默认用户维度结果排序器实现
 */
@Component
@RequiredArgsConstructor
public class DefaultUserDimResultSorter implements UserDimResultSorter {

    private static final Logger log = LoggerFactory.getLogger(DefaultUserDimResultSorter.class);
    private final XpdDimRuleMapper xpdDimRuleMapper;
    private final ActivityPerfResultConfMapper activityPerfResultConfMapper;
    private final XpdImportMapper xpdImportMapper;

    @Override
    public List<XpdUserDimResultsVO> sort(
            List<XpdUserDimResultsVO> userList,
            String sortDimId,
            String xSdDimId,
            String ySdDimId,
            String orgId,
            String xpdId) {

        if (StringUtils.isEmpty(sortDimId) || CollectionUtils.isEmpty(userList)) {
            return userList;
        }

        // 获取维度规则信息以确定排序类型
        XpdDimRulePO dimRule = xpdDimRuleMapper.getByXpdIdAndSdDimId(orgId, xpdId, sortDimId);
        if (dimRule == null) {
            return userList;
        }

        // 预加载绩效结果排序信息（如果需要）
        Map<String, Integer> perfResultOrderMap = Collections.emptyMap();
        if (Objects.equals(dimRule.getCalcType(), DimCalcTypeEnum.PERF_RESULT.getCode())) {
            perfResultOrderMap = loadPerfResultOrderMap(orgId, userList, sortDimId);
        }

        // 预判断是否为导入的分层结果（避免在比较器中重复查询）
        boolean isImportedDim = isImportedDimResult(dimRule);

        return userList.stream()
                .sorted(createUserComparator(sortDimId, xSdDimId, ySdDimId, dimRule, perfResultOrderMap, isImportedDim))
                .collect(Collectors.toList());
    }

    /**
     * 创建用户比较器
     */
    private Comparator<XpdUserDimResultsVO> createUserComparator(
            String sortDimId, String xSdDimId, String ySdDimId, XpdDimRulePO dimRule,
            Map<String, Integer> perfResultOrderMap, boolean isImportedDim) {

        return (user1, user2) -> {
            // 主要排序：根据选中维度的结果类型进行排序
            int primaryResult = comparePrimaryDimension(user1, user2, sortDimId, dimRule, perfResultOrderMap, isImportedDim);
            if (primaryResult != 0) {
                return primaryResult;
            }

            // 次要排序：x轴维度
            int xResult = compareSecondaryDimension(user1, user2, xSdDimId);
            if (xResult != 0) {
                return xResult;
            }

            // 第三排序：y轴维度
            return compareSecondaryDimension(user1, user2, ySdDimId);
        };
    }

    /**
     * 主要维度排序比较
     */
    private int comparePrimaryDimension(XpdUserDimResultsVO user1, XpdUserDimResultsVO user2,
                                       String sortDimId, XpdDimRulePO dimRule,
                                       Map<String, Integer> perfResultOrderMap, boolean isImportedDim) {

        XpdUserDimResultVO dimResult1 = findUserDimResult(user1, sortDimId);
        XpdUserDimResultVO dimResult2 = findUserDimResult(user2, sortDimId);

        if (dimResult1 == null && dimResult2 == null) {
            return 0;
        }
        if (dimResult1 == null) {
            return 1; // null值排在后面
        }
        if (dimResult2 == null) {
            return -1;
        }

        // 计算方式:0-按子维度结果计算 1-按指标结果计算 2-按绩效指标计算 3-按绩效得分计算
        Integer calcType = dimRule.getCalcType();
        // 根据计算类型确定排序方式
        if (Objects.equals(calcType, DimCalcTypeEnum.PERF_RESULT.getCode())) {
            // 绩效结果：根据绩效结果序号升序排序
            return compareByPerfResultOrder(dimResult1, dimResult2, perfResultOrderMap);
        } else if (Objects.equals(calcType, DimCalcTypeEnum.PERF_SCORE.getCode())) {
            // 绩效得分：根据绩效得分降序排序
            return compareByScore(dimResult1, dimResult2);
        } else if (isImportedDim) {
            // 导入的分层结果：根据分层序号降序排序
            return compareByGridLevelOrder(dimResult1, dimResult2);
        } else {
            // 得分或达标率：根据结果类型确定   结果类型:0-分值 1-达标率,非<绩效维度>下有效
            Integer resultType = dimRule.getResultType();
            if (Objects.equals(resultType, DimResultTypeEnum.SCORE_VALUE.getCode())) {
                // 得分：降序排序
                return compareByScore(dimResult1, dimResult2);
            } else {
                // 达标率：降序排序
                return compareByQualifiedRate(dimResult1, dimResult2);
            }
        }
    }

    /**
     * 次要维度排序比较（x轴、y轴维度）
     */
    private int compareSecondaryDimension(XpdUserDimResultsVO user1, XpdUserDimResultsVO user2, String dimId) {
        XpdUserDimResultVO dimResult1 = findUserDimResult(user1, dimId);
        XpdUserDimResultVO dimResult2 = findUserDimResult(user2, dimId);

        if (dimResult1 == null && dimResult2 == null) {
            return 0;
        }
        if (dimResult1 == null) {
            return 1;
        }
        if (dimResult2 == null) {
            return -1;
        }

        // x轴、y轴维度按分层序号降序排序
        return compareByGridLevelOrder(dimResult1, dimResult2);
    }

    /**
     * 根据绩效结果序号比较（升序）， 绩效结果序号越小，代表越好
     */
    private int compareByPerfResultOrder(XpdUserDimResultVO result1, XpdUserDimResultVO result2,
                                        Map<String, Integer> perfResultOrderMap) {
        // 获取绩效结果的排序信息
        Integer order1 = getPerfResultOrderIndex(result1, perfResultOrderMap);
        Integer order2 = getPerfResultOrderIndex(result2, perfResultOrderMap);

        if (order1 == null && order2 == null) {
            return 0;
        }
        if (order1 == null) {
            return 1;
        }
        if (order2 == null) {
            return -1;
        }

        return Integer.compare(order1, order2);
    }

    /**
     * 获取绩效结果的排序序号
     */
    private Integer getPerfResultOrderIndex(XpdUserDimResultVO result, Map<String, Integer> perfResultOrderMap) {
        // 直接使用perfResultId作为键获取排序序号
        String perfResultId = result.getPerfResultId();
        if (StringUtils.isEmpty(perfResultId)) {
            return null;
        }
        return perfResultOrderMap.get(perfResultId);
    }

    /**
     * 根据分层序号比较（降序）, 分层序号越大，代表越好
     */
    private int compareByGridLevelOrder(XpdUserDimResultVO result1, XpdUserDimResultVO result2) {
        Integer order1 = result1.getGridLevelOrderIndex();
        Integer order2 = result2.getGridLevelOrderIndex();

        if (order1 == null && order2 == null) {
            return 0;
        }
        if (order1 == null) {
            return 1;
        }
        if (order2 == null) {
            return -1;
        }

        return order2.compareTo(order1);
    }

    /**
     * 根据得分比较（降序）
     */
    private int compareByScore(XpdUserDimResultVO result1, XpdUserDimResultVO result2) {
        BigDecimal score1 = result1.getScoreValue();
        BigDecimal score2 = result2.getScoreValue();

        if (score1 == null && score2 == null) {
            return 0;
        }
        if (score1 == null) {
            return 1;
        }
        if (score2 == null) {
            return -1;
        }

        return score2.compareTo(score1); // 降序
    }

    /**
     * 根据达标率比较（降序）
     */
    private int compareByQualifiedRate(XpdUserDimResultVO result1, XpdUserDimResultVO result2) {
        BigDecimal rate1 = result1.getQualifiedPtg();
        BigDecimal rate2 = result2.getQualifiedPtg();

        if (rate1 == null && rate2 == null) {
            return 0;
        }
        if (rate1 == null) {
            return 1;
        }
        if (rate2 == null) {
            return -1;
        }

        return rate2.compareTo(rate1); // 降序
    }

    /**
     * 在用户维度结果中查找指定维度的结果
     */
    private XpdUserDimResultVO findUserDimResult(XpdUserDimResultsVO user, String dimId) {
        if (user == null || CollectionUtils.isEmpty(user.getUserDimResults())) {
            return null;
        }
        return user.getUserDimResults().stream()
                .filter(dim -> Objects.equals(dim.getSdDimId(), dimId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 加载绩效结果排序信息
     */
    private Map<String, Integer> loadPerfResultOrderMap(String orgId, List<XpdUserDimResultsVO> userList, String sortDimId) {
        // 收集所有绩效结果ID
        Set<String> perfResultIds = new HashSet<>();
        userList.forEach(user -> {
            XpdUserDimResultVO dimResult = findUserDimResult(user, sortDimId);
            if (dimResult != null && StringUtils.isNotEmpty(dimResult.getPerfResultId())) {
                perfResultIds.add(dimResult.getPerfResultId());
            }
        });

        if (perfResultIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 查询绩效结果配置
        List<ActivityPerfResultConfPO> perfResultConfs = activityPerfResultConfMapper.findByOrgIdAndIds(
                orgId, new ArrayList<>(perfResultIds));

        // 构建绩效结果ID到排序序号的映射
        return perfResultConfs.stream()
                .filter(conf -> conf.getOrderNum() != null)
                .collect(Collectors.toMap(
                        ActivityPerfResultConfPO::getId,
                        ActivityPerfResultConfPO::getOrderNum,
                        (existing, replacement) -> existing // 处理重复键
                ));
    }

    /**
     * 判断是否为导入的分层结果
     */
    private boolean isImportedDimResult(XpdDimRulePO dimRule) {
        // 检查是否存在对应的导入记录
        List<XpdImportPO> imports = xpdImportMapper.listBySdDimIdsAndImportType(
                dimRule.getOrgId(), dimRule.getXpdId(),
                Collections.singletonList(dimRule.getSdDimId()),
                XpdImportTypeEnum.DIM.getCode());
        return CollectionUtils.isNotEmpty(imports);
    }
}
