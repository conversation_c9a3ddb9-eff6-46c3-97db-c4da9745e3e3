package com.yxt.talent.rv.application.dmp;

import com.yxt.ApplicationCommandService;
import com.yxt.common.service.ILock;
import com.yxt.common.service.LockService;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.rv.application.dmp.calc.DmpCalculator;
import com.yxt.talent.rv.application.user.UserTransferComponent;
import com.yxt.talent.rv.domain.dmp.Dmp;
import com.yxt.talent.rv.domain.dmp.DmpUser;
import com.yxt.talent.rv.domain.dmp.event.DmpCalcTaskMessageEvent;
import com.yxt.talent.rv.domain.dmp.repo.DmpDomainRepo;
import com.yxt.talent.rv.domain.dmp.repo.DmpUserDomainRepo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpPermMapper;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SpevalAclServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.util.BeanHelper.bean2Json;
import static com.yxt.talent.rv.infrastructure.common.constant.AppConstants.TRANSFERABLE_RESOURCES_CODE_DMP;

/**
 * 用于增删改的Application Service，基于CQRS命令和查询责任分离的模式，
 * 该类中的方法因为涉及到领域对象和实体状态的变更，以及跨领域事件的流转，
 * 所以只能依赖领域模型层，不能直接依赖底层存储mapper
 * 【注意】CommandAppService和QueryAppService之间不应相互依赖，如果有两边通用的逻辑，应该抽取到XxxAppComponent中
 */
@Slf4j
@RequiredArgsConstructor
@ApplicationCommandService
public class DmpCmdAppService {

    private final DmpUserDomainRepo dmpUserDomainRepo;
    private final DmpDomainRepo dmpDomainRepo;
    private final SpevalAclService spevalAclService;
    private final DmpCalculator dmpCalculator;
    private final ILock lockService;
    private final DmpPermMapper dmpPermMapper;
    private final DmpMapper dmpMapper;
    private final UserTransferComponent userTransferComponent;
    private final RocketMqAclSender rocketMqAclSender;

    /**
     * 处理用户被取消奇点产品的标识事件
     *
     * @param orgId
     * @param userId
     */
    public void dealWhenCancelUserSpProductCode(String orgId, String userId) {
        // 删除dmpUser等相关实体
        DmpUser.LoadConfig loadConfig = DmpUser.LoadConfig.WITH_ALL;
        Collection<DmpUser> dmpUsers = dmpUserDomainRepo.loadByUserId(orgId, userId, loadConfig);
        dmpUsers.forEach(DmpUser::delete);
        dmpUserDomainRepo.delete(dmpUsers);
        // 从表单任务中删除该员工
        Set<String> dmpIds = StreamUtil.map2set(dmpUsers, DmpUser::getDmpId);
        dmpIds.forEach(dmpId -> dmpDomainRepo.load(orgId, dmpId, Dmp.LoadConfig.WITH_ALL)
                .ifPresent(dmp -> this.removeFormUser(dmp, userId)));
    }

    /**
     * 从项目表单任务中删除被测评人
     *
     * @param dmp
     * @param userId
     */
    public void removeFormUser(Dmp dmp, String userId) {
        spevalAclService.batchDelEvalUsers(dmp.getId(), List.of(userId), dmp.getOrgId(), "event");
    }

    /**
     * 计算员工的维度维度匹配结果和用户胜任情况
     *
     * @param dmpCalcTaskMessageEvent
     */
    public void calculateDmpTaskAndUserResult(DmpCalcTaskMessageEvent dmpCalcTaskMessageEvent) {
        // 遍历计算用户在各个任务维度下的匹配情况
        dmpCalculator.calculateDmpTask(dmpCalcTaskMessageEvent);

        // 计算员工的胜任结果(需要确认所有任务都计算完成之后，才能计算员工胜任结果)
        String orgId = dmpCalcTaskMessageEvent.getOrgId();
        String dmpId = dmpCalcTaskMessageEvent.getDmpId();
        String coordinatorId = dmpCalcTaskMessageEvent.getCoordinatorId();

        if (shouldCalculate(coordinatorId)) {
            log.info("LOG13085:{}", bean2Json(dmpCalcTaskMessageEvent, ALWAYS));
            dmpCalculator.calculateDmpUserResult(orgId, dmpId, Collections.emptyList());
        }
    }

    private boolean shouldCalculate(String coordinatorId) {
        if (StringUtils.isBlank(coordinatorId)) {
            log.info("LOG13095:协调者不存在，直接计算，防止错误阻止");
            return true;
        }
        RedissonClient redissonClient =
                Objects.requireNonNull(((LockService) lockService).getRedissonClient());
        RAtomicLong atomicLong = redissonClient.getAtomicLong(coordinatorId);
        // 为了避免限制太死导致不计算，这里当发现协调者不存在时，也执行一次计算，聊胜于无
        long unfinishedTaskCnt = -999;
        boolean shouldCalculate = atomicLong == null || (unfinishedTaskCnt = atomicLong.get()) <= 0;
        log.info("LOG13075:coordinatorId={}, unfinishedTaskCnt={}", coordinatorId,
                unfinishedTaskCnt);
        return shouldCalculate;
    }

    /**
     * 处理用户资源转移事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void transferResource(String orgId, String fromUserId, String toUserId) {
        userTransferComponent.transferResource(orgId, fromUserId, toUserId,
                dmpMapper::countByUserScore, () -> {
                    dmpMapper.transferResource(orgId, fromUserId, toUserId);
                    // 替换管理者（管理者可选多个，有可能会出现替换之后管理者重复的情况，所以要特殊处理）
                    dmpPermMapper.transferResourceStep1(orgId, fromUserId, toUserId);
                    dmpPermMapper.transferResourceStep2(orgId, fromUserId, toUserId);
                }, TRANSFERABLE_RESOURCES_CODE_DMP);
    }

}
