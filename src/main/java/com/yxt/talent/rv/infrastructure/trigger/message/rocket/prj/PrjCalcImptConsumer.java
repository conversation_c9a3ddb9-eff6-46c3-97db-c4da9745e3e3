package com.yxt.talent.rv.infrastructure.trigger.message.rocket.prj;

import com.yxt.common.util.BeanHelper;
import com.yxt.event.EventPublisher;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.rv.domain.prj.event.PrjCalcImptMessageEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_PSI_C_PRJ_CALC_EXPORT;

/**
 * 项目盘点-按照维度导入的数据-计算
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(         consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_PSI_C_PRJ_CALC_EXPORT,         topic = TOPIC_PSI_C_PRJ_CALC_EXPORT,         consumeThreadNumber = 2, consumeTimeout = 30)
public class PrjCalcImptConsumer implements RocketMQListener<PrjCalcImptMessageEvent> {

    private final EventPublisher eventPublisher;

    @Override
    public void onMessage(PrjCalcImptMessageEvent message) {
        try {
            log.debug("LOG12925:{}", BeanHelper.bean2Json(message, ALWAYS));
            eventPublisher.publish(message);
        } catch (Throwable e) {
            log.error("LOG65290:", e);
        }
    }

}
