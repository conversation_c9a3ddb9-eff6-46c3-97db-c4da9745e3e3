package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.rv.application.prj.user.dto.PrjUserCountDTO;
import com.yxt.talent.rv.application.prj.user.dto.PrjUserDimDTO;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.general.prj.viewobj.PrjSubUserClientVO;
import com.yxt.talent.rv.controller.manage.prj.user.query.PrjUserQuery;
import com.yxt.talent.rv.controller.manage.prj.user.query.PrjUserSearchQuery;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjResultSubUserVO;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserBaseVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper
@Repository
public interface PrjUserMapper extends CommonMapper<PrjUserPO> {

    @Nonnull
    IPage<PrjUserDimDTO> searchPage(
            Page<PrjUserDimDTO> page, @Nonnull @Param("orgId") String orgId,
            @Param("projectId") String projectId, @Nonnull @Param("search") PrjUserQuery search,
            @Param("qwUserIds") List<String> qwUserIds);

    @Nonnull
    List<PrjUserDimDTO> search(
            @Nonnull @Param("orgId") String orgId, @Param("projectId") String projectId,
            @Nonnull @Param("search") PrjUserQuery search,
            @Nullable @Param("qwUserIds") List<String> qwUserIds);

    @Nonnull
    List<PrjUserPO> selectByPrjId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("prjId") String prjId);

    @Nonnull
    List<PrjUserBaseVO> selectSimpleByPrjId(
            @Nonnull @Param("orgId") String orgId, @Param("projectId") String projectId);

    @Nonnull
    List<String> selectUserIdByPrjId(
            @Nonnull @Param("orgId") String orgId, @Param("projectId") String projectId);

    /**
     * 统计用户被盘点的次数
     *
     * @param orgId
     * @param userIds
     */
    @Nonnull
    List<PrjUserCountDTO> countByUserIds(
            @Nonnull @Param("orgId") String orgId, @Param("userIds") List<String> userIds);

    /**
     * 【专用查询】 用户端盘点结果【人员维度】->人员查看界面列表 <br>
     * 查询用户参与过的盘点项目
     */
    @Nonnull
    IPage<PrjResultSubUserVO> selectUserParticipatedPrj(
            Page<PrjResultSubUserVO> pageable, @Nonnull @Param("orgId") String orgId,
            @Nonnull @Param("userId") String userId);

    /**
     * 【专用查询】 用户端盘点结果【项目维度】-> 我管辖的人员 <br>
     * 查询某项目中我管辖的人员
     */
    @Nonnull
    IPage<PrjSubUserClientVO> selectMyScopeUserInPrj(
            Page<PrjSubUserClientVO> pageable, @Nonnull @Param("orgId") String orgId,
            @Param("projectId") String projectId, @Param("keyword") String keyword,
            @Nonnull @Param("search") TeamScopeAuthClientQuery search,
            @Param("qwUserIds") List<String> qwUserIds);

    @Nonnull
    IPage<PrjUserPO> listPage(
            @Param("criteria") PrjUserSearchQuery criteria, Page<PrjUserPO> page);

    long batchInsertOrUpdate(@Nonnull @Param("list") List<PrjUserPO> list);

    default void insertOrUpdate(@Nonnull PrjUserPO prjUserPO) {
        batchInsertOrUpdate(List.of(prjUserPO));
    }

    default void insertOrUpdateBatch(Collection<PrjUserPO> entities) {
        batchExecute(entities, this::batchInsertOrUpdate);
    }

    void deleteByOrgIdAndId(@Param("orgId") String orgId, @Param("id") String id);

    void deleteBatch(@Param("orgId") String orgId, @Param("removeIds") Collection<String> removeIds);

    @Nullable
    PrjUserPO selectByOrgIdAndId(@Param("orgId") String orgId, @Param("id") String id);

    void deleteByOrgIdAndPrjIdAndUserIds(
            @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("userIds") List<String> userIds);

    List<PrjUserPO> selectByOrgIdAndPrjIdOrderByUpdateTimeDesc(
            @Param("orgId") String orgId, @Param("prjId") String prjId);

    List<PrjUserPO> selectByPrjIdOrderByCreateTime(
            @Param("orgId") String orgId, @Param("prjId") String prjId);

    List<PrjUserPO> selectByOrgId(@Param("orgId") String orgId);

    List<PrjUserPO> selectByOrgIdAndPrjIdAndUserIds(
            @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("userIds") Collection<String> userIds);

    @Nullable
    PrjUserPO selectByOrgIdAndPrjIdAndUserId(
            @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("userId") String userId);

    int countByOrgIdAndResult( @Param("orgId") String orgId, @Param("prjResult") int prjResult);

    void cleanPrjResult(@Param("orgIds") Set<String> orgIds);

    int findRvUserCount(@Param("orgId") String orgId, @Param("projectId") String projectId);

    List<String> findUserByUserId(@Param("orgId") String  orgId,
            @Param("projectIds") List<String> projectIds,
            @Param("userIds") List<String> userIds);
}
