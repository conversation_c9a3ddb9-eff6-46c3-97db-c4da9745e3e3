package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.result;

import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema
public class RuleOperatorInfo {

    @Schema(description = "运算类型 1：等于 2：不等于 3：大于 4：小于 5：大于等于 6：小于等于",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = ExceptionKeys.PRJ_LABEL_RULE_OPERATOR_NOT_BLANK)
    private Integer operateType;

    @Schema(description = "类型or数值：1-数值 2-类型")
    private Integer operateRadio;

    @Schema(description = "运算符中文，如：大于，小于，大于等于，小于等于，等于等等")
    private String operatorCnName;

    @Schema(description = "运算符后的值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = ExceptionKeys.PRJ_LABEL_RULE_VALUE_NOT_NULL)
    private Object value;
}
