package com.yxt.talent.rv.infrastructure.trigger.message.rocket.dmp;

import com.alibaba.fastjson.JSON;
import com.yxt.event.EventPublisher;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.rv.domain.dmp.event.DmpAutoAddUserMsgEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static org.apache.rocketmq.spring.annotation.ConsumeMode.ORDERLY;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_PSI_C_DMP_ADD_USER;

/**
 * @Description 用户组加人初始化
 *
 * <AUTHOR>
 * @Date 2024/5/7 17:14
 **/
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(         consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_PSI_C_DMP_ADD_USER,         topic = TOPIC_PSI_C_DMP_ADD_USER,         consumeMode = ORDERLY, consumeTimeout = 30)
public class DmpAddUserInitConsumer implements RocketMQListener<DmpAutoAddUserMsgEvent> {


    private final EventPublisher eventPublisher;

    @Override
    public void onMessage(DmpAutoAddUserMsgEvent event) {
        log.info("LOG67000:DmpAddUserInitConsumer start={}", JSON.toJSONString(event));
        try {
            eventPublisher.publish(event);
        } catch (Exception e){
            log.error("LOG14635:DmpAddUserInitConsumer error msg:", e);
        }

        log.info("LOG13285:DmpAddUserInitConsumer end={}", JSON.toJSONString(event));
    }
}
