package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO;
import com.yxt.talent.rv.infrastructure.repository.xpd.RuleConfigByCaliDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

public interface XpdDimMapper extends CommonMapper<XpdDimPO> {

    List<XpdDimPO> selectByOrgId(@Param("orgId") String orgId);

    int insert(XpdDimPO record);

    XpdDimPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdDimPO record);

    int updateBatch(@Param("list") List<XpdDimPO> list);

    int batchInsert(@Param("list") List<XpdDimPO> list);

    void batchUpdateScore(List<XpdDimPO> list);

    @Update("update rv_xpd_dim set score_total = null where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0")
    int clearScoreByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    /**
     * 过滤删除的数据
     *
     * @param id 主键
     * @return XpdDimPO
     */
    XpdDimPO selectById(String id);

    XpdDimPO selectByXpdIdAndSdDimId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("sdDimId") String sdDimId);

    List<XpdDimPO> selectByXpdIdAndSdDimIds(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("sdDimIds") List<String> sdDimIds);

    List<XpdDimPO> listByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    Collection<XpdDimPO> selectByDimIds(
        @Param("orgId") String orgId, @Param("sdDimIds") Collection<String> sdDimIds);

    /**
     * 逻辑删除
     *
     * @param orgId
     * @param sdDimIds
     */
    void deleteBySdDimIds(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("sdDimIds") List<String> sdDimIds,
        @Param("userId") String userId);

    void deleteByXpdId(
        @Param("orgId") String orgId,
        @Param("userId") String userId,
        @Param("xpdId") String xpdId);

    List<XpdDimPO> selectByXpdId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("excludePerf") Integer excludePerf);

    IPage<XpdDimPO> selectByXpdIdPage(
        @Param("page") IPage<XpdDimPO> page, @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("excludePerf") Integer excludePerf,
        @Param("searchDimIds") List<String> searchDimIds);

    int countByOrgIdAndXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    @Select("""
    select sum(formula_flag) as formula_qty,sum(threshold_invalid) as invalid_qty
    from
    (
    select xd.xpd_id,if(xdr.calc_type = 1 and xdr.calc_rule = 1, 1, 0) as formula_flag,xdr.threshold_invalid from rv_xpd_dim xd
    join rv_xpd_dim_rule xdr on xdr.org_id = xd.org_id and xdr.xpd_id = xd.xpd_id and xdr.sd_dim_id = xd.sd_dim_id and xdr.deleted = 0
    left join rv_xpd_import xi on xi.org_id = xd.org_id and xi.xpd_id = xd.xpd_id and xi.sd_dim_id = xd.sd_dim_id and xi.deleted = 0 and xi.import_type = 1
    where xd.org_id = #{orgId}
    and xd.deleted = 0 and xi.id is null
    and xd.xpd_id = #{xpdId}
    union all
    select xpd_id,if(calc_type = 1 and result_type = 1 and calc_rule = 1, 1, 0) as formula_flag,threshold_invalid
    from rv_xpd_rule where org_id = #{orgId}
    and xpd_id = #{xpdId} and deleted = 0
    ) as t
    """)
    RuleConfigByCaliDto ruleConfigByCali(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    void insertOrUpdate(XpdDimPO xpdDimPO);
}