package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.application.prj.prj.dto.PrjCompletedUserDTO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.query.DeptProjectClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjClientVO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjUserClientVO;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamPrjResultScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.general.prj.viewobj.PrjClientVO;
import com.yxt.talent.rv.controller.manage.prj.prj.query.PrjQuery;
import com.yxt.talent.rv.controller.manage.prj.prj.viewobj.PrjListVO;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserResultTrackVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface PrjMapper extends CommonMapper<PrjPO> {

    int insert(@Nonnull PrjPO entity);

    int insertList(@Nonnull @Param("list") List<PrjPO> list);

    long insertOrUpdate(@Nonnull PrjPO entity);

    int updateById(@Nonnull PrjPO entity);

    @Nullable
    PrjPO selectProjectByName(
            @Nonnull @Param("orgId") String orgId, @Param("projectName") String projectName);

    @Nonnull
    IPage<PrjListVO> search(
            @Nonnull IPage<PrjListVO> page, @Nonnull @Param("search") PrjQuery search,
            @Nonnull @Param("orgId") String orgId);

    @Nonnull
    List<String> queryPrjNameByModelId(
            @Nonnull @Param("orgId") String orgId, @Param("modelId") String modelId);

    @Nonnull
    List<String> queryPrjNameByModelIds(
            @Nonnull @Param("orgId") String orgId, @Param("modelIds") List<String> modelIds);

    int queryPrjCountByModelId(
            @Nonnull @Param("orgId") String orgId, @Param("modelId") String modelId);

    /**
     * 用户端-盘点结果【项目维度】列表 -【专用】
     */
    @Nonnull
    IPage<PrjClientVO> selectPrjClientPage(
            @Param("page") IPage<PrjClientVO> page, @Nonnull @Param("orgId") String orgId,
            @Param("prjStatusList") List<Integer> prjStatusList,
            @Nonnull @Param("search") TeamPrjResultScopeAuthClientQuery search);

    @Nonnull
    List<PrjUserResultTrackVO> selectByUserId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId);

    @Nonnull
    List<PrjPO> selectLastProjectId(@Nonnull @Param("orgId") String orgId);

    @Nonnull
    IPage<DeptPrjClientVO> selectMineProjects(
            @Param("page") IPage<DeptPrjClientVO> page, @Nonnull @Param("orgId") String orgId,
            @Nonnull @Param("userId") String userId,
            @Param("criteria") DeptProjectClientQuery criteria);

    @Nullable
    DeptPrjClientVO selectMineProjectStatistics(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId,
            @Param("criteria") DeptProjectClientQuery criteria);

    @Nonnull
    List<PrjPO> selectRefreshablePrj();

    /**
     * 统计部门经理所管辖下的人员的已完成盘点人数
     *
     * @param orgId
     * @param projectIds
     * @param criteria
     */
    @Nonnull
    List<PrjCompletedUserDTO> selectProjectCompletedUsers(
            @Nonnull @Param("orgId") String orgId, @Param("projectIds") List<String> projectIds,
            @Param("criteria") DeptProjectClientQuery criteria);

    @Nonnull
    IPage<DeptPrjUserClientVO> selectMineProjectUser(
            @Param("page") IPage<DeptPrjUserClientVO> page, @Nonnull @Param("orgId") String orgId,
            @Nonnull @Param("userId") String userId,
            @Param("criteria") DeptProjectClientQuery criteria);

    @Nonnull
    List<String> selectScopedPrjUserIds(
            @Nonnull @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Param("criteria") DeptProjectClientQuery criteria);

    @Nullable
    PrjPO selectByOrgIdAndId(@Nonnull @Param("orgId") String orgId, @Param("prjId") String prjId);

    @Nonnull
    List<String> selectDistinctCreateUserId(@Nonnull @Param("orgId") String orgId);

    @Nonnull
    List<PrjPO> selectByCatId(@Nonnull @Param("orgId") String orgId, @Param("catId") String catId);

    @Nonnull
    List<PrjPO> selectByOrgId(@Nonnull @Param("orgId") String orgId);

    List<String> queryOrgIds();

    /**
     * 该员工创建和负责的盘点项目数量，去重
     *
     * @param orgId
     * @param userId
     * @return
     */
    int countByUserScope(@Param("orgId") String orgId, @Param("userId") String userId);

    /**
     * 更换资源的所有人
     * @param orgId
     * @param fromUserId
     * @param toUserId
     */
    void transferResource(String orgId, String fromUserId, String toUserId);
}
